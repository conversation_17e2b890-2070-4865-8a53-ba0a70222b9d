import React from "react";
import {
  View,
  TextInput as RNTextInput,
  Text,
  StyleSheet,
  TextInputProps as RNTextInputProps,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";

interface OptimizedTextInputProps extends RNTextInputProps {
  label?: string;
  error?: boolean;
  errorText?: string;
  leftIcon?: string;
  rightIcon?: string;
  mode?: "outlined" | "flat";
  disabled?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
}

export const OptimizedTextInput: React.FC<OptimizedTextInputProps> = ({
  label,
  error = false,
  errorText,
  leftIcon,
  rightIcon,
  mode = "outlined",
  disabled = false,
  style,
  multiline = false,
  numberOfLines = 1,
  ...props
}) => {
  const { colors } = useTheme();

  const inputStyle = [
    styles.input,
    mode === "outlined" ? styles.outlined : null,
    mode === "flat" ? styles.flat : null,
    error ? styles.error : null,
    disabled ? styles.disabled : null,
    multiline ? { height: numberOfLines * 20 + 32 } : null,
    leftIcon ? { paddingLeft: 40 } : null,
    rightIcon ? { paddingRight: 40 } : null,
    {
      backgroundColor: colors.SURFACE,
      borderColor: error ? "#B00020" : colors.BORDER,
      color: colors.TEXT_PRIMARY,
    },
    style,
  ].filter((s) => s !== null);

  const labelStyle = [
    styles.label,
    error && styles.errorLabel,
    { color: error ? "#B00020" : colors.TEXT_SECONDARY },
  ];

  return (
    <View style={styles.container}>
      {label && <Text style={labelStyle}>{label}</Text>}
      <View style={styles.inputContainer}>
        {leftIcon && (
          <MaterialCommunityIcons
            name={leftIcon as any}
            size={20}
            color={colors.TEXT_SECONDARY}
            style={styles.leftIcon}
          />
        )}
        <RNTextInput
          style={inputStyle}
          placeholderTextColor={colors.TEXT_HINT}
          editable={!disabled}
          multiline={multiline}
          numberOfLines={numberOfLines}
          textAlignVertical={multiline ? "top" : "center"}
          {...props}
        />
        {rightIcon && (
          <MaterialCommunityIcons
            name={rightIcon as any}
            size={20}
            color={colors.TEXT_SECONDARY}
            style={styles.rightIcon}
          />
        )}
      </View>
      {error && errorText && (
        <Text style={[styles.errorText, { color: "#B00020" }]}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 4,
    marginLeft: 4,
  },
  errorLabel: {
    color: "#B00020",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 4,
    minHeight: 48,
  },
  outlined: {
    borderWidth: 1,
  },
  flat: {
    borderBottomWidth: 1,
    borderRadius: 0,
  },
  error: {
    borderColor: "#B00020",
  },
  disabled: {
    opacity: 0.6,
  },
  leftIcon: {
    position: "absolute",
    left: 12,
    top: 18,
    zIndex: 1,
  },
  rightIcon: {
    position: "absolute",
    right: 12,
    zIndex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
});
