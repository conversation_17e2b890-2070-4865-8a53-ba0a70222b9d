import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from "react-native";
import { Text, TextInput, ActivityIndicator } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";

import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors } from "../../theme";
import {
  createUserWithEmailAndPassword,
  sendEmailVerification,
} from "firebase/auth";
import { auth } from "../../../config/firebase";
import {
  signInWithGoogle,
  getGoogleSignInErrorMessage,
} from "../../utils/googleAuthExpo";
import { useLanguage } from "../../context/LanguageContext";
import { addUser } from "../../services/UserServices";

const SignUpScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { t } = useLanguage();

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const handleSignUp = async () => {
    if (!name || !email || !password || !confirmPassword) {
      Alert.alert("Lỗi", "Vui lòng nhập đầy đủ thông tin");
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert("Lỗi", "Mật khẩu không khớp");
      return;
    }

    // Kiểm tra định dạng email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert("Lỗi", "Email không hợp lệ");
      return;
    }

    // Kiểm tra độ dài mật khẩu
    if (password.length < 6) {
      Alert.alert("Lỗi", "Mật khẩu phải có ít nhất 6 ký tự");
      return;
    }
    setIsLoading(true);
    try {
      // Tạo tài khoản trước
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Đưa user vào collection users
      await addUser({
        uid: user.uid,
        email: user.email || "",
        displayName: name,
        phone: "", // Nếu có trường nhập số điện thoại, thay thế ở đây
      });

      // Gửi email xác thực
      await sendEmailVerification(user);

      setIsLoading(false);

      // Đăng xuất ngay sau khi gửi email xác thực
      await auth.signOut();

      Alert.alert(
        "Đăng ký thành công",
        "Vui lòng kiểm tra email để xác thực tài khoản của bạn trước khi đăng nhập.",
        [
          {
            text: "OK",
            onPress: () => navigation.navigate("Login"),
          },
        ]
      );
    } catch (error: any) {
      setIsLoading(false);
      let errorMessage = t("auth.signupError");
      if (error.code === "auth/email-already-in-use") {
        errorMessage = t("auth.emailAlreadyInUse");
      } else if (error.code === "auth/invalid-email") {
        errorMessage = t("auth.invalidEmail");
      } else if (error.code === "auth/weak-password") {
        errorMessage = t("auth.weakPassword");
      }
      Alert.alert(t("common.error"), errorMessage);
      console.error("Error creating user:", error.message);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    try {
      const result = await signInWithGoogle();

      if (result) {
        console.log("Google Sign-Up successful:", result.user.email);

        Alert.alert(
          t("common.success"),
          result.additionalUserInfo?.isNewUser
            ? t("auth.accountCreatedWithGoogle")
            : t("auth.googleSignInSuccess")
        );
      }
    } catch (error: any) {
      console.error("Google Sign-Up Error:", error);
      const errorMessage = getGoogleSignInErrorMessage(error, t);
      Alert.alert(t("common.error"), errorMessage);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/signup.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.title}>{t("auth.createAccount")}</Text>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.fullName")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.fullNamePlaceholder")}
                value={name}
                onChangeText={setName}
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.email")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.emailPlaceholder")}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.password")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.passwordPlaceholder")}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
                textColor={Colors.TEXT_PRIMARY}
                right={
                  <TextInput.Icon
                    icon={showPassword ? "eye-off" : "eye"}
                    onPress={() => setShowPassword(!showPassword)}
                    color={Colors.GRAY_DARK}
                  />
                }
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.confirmPassword")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.confirmPasswordPlaceholder")}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
                textColor={Colors.TEXT_PRIMARY}
                right={
                  <TextInput.Icon
                    icon={showConfirmPassword ? "eye-off" : "eye"}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    color={Colors.GRAY_DARK}
                  />
                }
              />
            </View>

            <TouchableOpacity
              style={styles.signUpButton}
              onPress={handleSignUp}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.WHITE} />
              ) : (
                <Text style={styles.signUpButtonText}>{t("auth.signup")}</Text>
              )}
            </TouchableOpacity>

            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>{t("auth.or")}</Text>
              <View style={styles.divider} />
            </View>

            <TouchableOpacity
              style={styles.googleButton}
              onPress={handleGoogleSignUp}
              disabled={isGoogleLoading || isLoading}
            >
              {isGoogleLoading ? (
                <ActivityIndicator color={Colors.TEXT_PRIMARY} />
              ) : (
                <>
                  <Image
                    source={require("../../../assets/icons/google.png")}
                    style={styles.googleIcon}
                  />
                  <Text style={styles.googleButtonText}>
                    {t("auth.signUpWithGoogle")}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>
                {t("auth.alreadyHaveAccount")}
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                <Text style={styles.loginLink}>{t("auth.login")}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 350,
    height: 150,
  },
  formContainer: {
    flex: 1,
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 20,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 20,
    marginTop: 10,
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 5,
    marginLeft: 5,
  },
  input: {
    backgroundColor: Colors.WHITE,
  },
  signUpButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginTop: 10,
    marginBottom: 20,
  },
  signUpButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  dividerText: {
    color: Colors.TEXT_SECONDARY,
    paddingHorizontal: 10,
  },
  googleButton: {
    borderWidth: 1,
    borderColor: Colors.GRAY_MEDIUM,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "center",
  },
  googleIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  googleButtonText: {
    color: Colors.TEXT_PRIMARY,
    fontSize: 16,
    fontWeight: "500",
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
  },
  loginText: {
    color: Colors.TEXT_SECONDARY,
    fontSize: 14,
    marginRight: 5,
  },
  loginLink: {
    color: Colors.PRIMARY,
    fontSize: 14,
    fontWeight: "bold",
  },
});

export default SignUpScreen;
