import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from "react-native";
import { Text, TextInput } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";

import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { SPACING } from "../../theme/spacing";
import {
  createUserWithEmailAndPassword,
  sendEmailVerification,
} from "firebase/auth";
import { auth } from "../../../config/firebase";
import {
  signInWithGoogle,
  getGoogleSignInErrorMessage,
} from "../../utils/googleAuthExpo";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { addUser } from "../../services/UserServices";
import { GradientBackground } from "../../components/common/GradientBackground";
import { ModernButton } from "../../components/common/ModernButton";
import { ModernCard } from "../../components/common/ModernCard";

const SignUpScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { t } = useLanguage();
  const { colors } = useTheme();

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [error, setError] = useState("");
  const handleSignUp = async () => {
    if (!name || !email || !password || !confirmPassword) {
      setError("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    if (password !== confirmPassword) {
      setError("Mật khẩu không khớp");
      return;
    }

    // Kiểm tra định dạng email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError("Email không hợp lệ");
      return;
    }

    // Kiểm tra độ dài mật khẩu
    if (password.length < 6) {
      setError("Mật khẩu phải có ít nhất 6 ký tự");
      return;
    }
    setIsLoading(true);
    setError("");
    try {
      // Tạo tài khoản trước
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Đưa user vào collection users
      await addUser({
        uid: user.uid,
        email: user.email || "",
        displayName: name,
        phone: "", // Nếu có trường nhập số điện thoại, thay thế ở đây
      });

      // Gửi email xác thực
      await sendEmailVerification(user);

      setIsLoading(false);

      // Đăng xuất ngay sau khi gửi email xác thực
      await auth.signOut();

      Alert.alert(
        "Đăng ký thành công",
        "Vui lòng kiểm tra email để xác thực tài khoản của bạn trước khi đăng nhập.",
        [
          {
            text: "OK",
            onPress: () => navigation.navigate("Login"),
          },
        ]
      );
    } catch (error: any) {
      setIsLoading(false);
      let errorMessage = t("auth.signupError");
      if (error.code === "auth/email-already-in-use") {
        errorMessage = t("auth.emailAlreadyInUse");
      } else if (error.code === "auth/invalid-email") {
        errorMessage = t("auth.invalidEmail");
      } else if (error.code === "auth/weak-password") {
        errorMessage = t("auth.weakPassword");
      }
      setError(errorMessage);
      console.error("Error creating user:", error.message);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    setError("");
    try {
      const result = await signInWithGoogle();

      if (result) {
        console.log("Google Sign-Up successful:", result.user.email);
        // Navigation will be handled automatically by RootNavigator
      }
    } catch (error: any) {
      console.error("Google Sign-Up Error:", error);
      const errorMessage = getGoogleSignInErrorMessage(error, t);
      setError(errorMessage);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/welcome.png")}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.welcomeTitle}>{t("auth.createAccount")}</Text>
          <Text style={styles.welcomeSubtitle}>{t("auth.joinUsToday")}</Text>
        </View>
      </SafeAreaView>

      <ModernCard
        style={styles.formContainer}
        variant="elevated"
        borderRadius="xl"
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.scrollView}
        >
          {error ? (
            <ModernCard variant="outlined" style={styles.errorCard}>
              <Text style={styles.errorText}>{error}</Text>
            </ModernCard>
          ) : null}

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t("auth.fullName")}</Text>
            <TextInput
              style={styles.input}
              placeholder={t("auth.fullNamePlaceholder")}
              value={name}
              onChangeText={(text) => {
                setName(text);
                setError("");
              }}
              mode="outlined"
              outlineColor={colors.BORDER}
              activeOutlineColor={Colors.PRIMARY}
              textColor={colors.TEXT_PRIMARY}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t("auth.email")}</Text>
            <TextInput
              style={styles.input}
              placeholder={t("auth.emailPlaceholder")}
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                setError("");
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              mode="outlined"
              outlineColor={colors.BORDER}
              activeOutlineColor={Colors.PRIMARY}
              textColor={colors.TEXT_PRIMARY}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t("auth.password")}</Text>
            <TextInput
              style={styles.input}
              placeholder={t("auth.passwordPlaceholder")}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              mode="outlined"
              outlineColor={Colors.GRAY_MEDIUM}
              activeOutlineColor={Colors.PRIMARY}
              textColor={Colors.TEXT_PRIMARY}
              right={
                <TextInput.Icon
                  icon={showPassword ? "eye-off" : "eye"}
                  onPress={() => setShowPassword(!showPassword)}
                  color={Colors.GRAY_DARK}
                />
              }
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t("auth.confirmPassword")}</Text>
            <TextInput
              style={styles.input}
              placeholder={t("auth.confirmPasswordPlaceholder")}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              autoCapitalize="none"
              mode="outlined"
              outlineColor={Colors.GRAY_MEDIUM}
              activeOutlineColor={Colors.PRIMARY}
              textColor={Colors.TEXT_PRIMARY}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? "eye-off" : "eye"}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  color={Colors.GRAY_DARK}
                />
              }
            />
          </View>

          <ModernButton
            title={t("auth.signup")}
            variant="gradient"
            size="large"
            onPress={handleSignUp}
            loading={isLoading}
            disabled={isLoading}
            style={styles.signUpButton}
            fullWidth
          />

          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.dividerText}>{t("auth.or")}</Text>
            <View style={styles.divider} />
          </View>

          <TouchableOpacity
            style={styles.googleButton}
            onPress={handleGoogleSignUp}
            disabled={isGoogleLoading || isLoading}
          >
            <View style={styles.googleButtonContent}>
              <Image
                source={require("../../../assets/icons/google.png")}
                style={styles.googleIcon}
              />
              <Text style={styles.googleButtonText}>
                {t("auth.signUpWithGoogle")}
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>{t("auth.alreadyHaveAccount")}</Text>
            <TouchableOpacity onPress={() => navigation.navigate("Login")}>
              <Text style={styles.loginLink}>{t("auth.login")}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ModernCard>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  backButton: {
    position: "absolute",
    top: SPACING.md,
    left: SPACING.lg,
    zIndex: 1,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  backButtonText: {
    fontSize: 24,
    color: Colors.WHITE,
    fontWeight: "bold",
  },
  logoContainer: {
    alignItems: "center",
    paddingTop: SPACING.xxxl,
    paddingBottom: SPACING.xl,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: SPACING.lg,
  },
  welcomeTitle: {
    ...Typography.TEXT_STYLES.h1,
    color: Colors.WHITE,
    textAlign: "center",
    marginBottom: SPACING.sm,
  },
  welcomeSubtitle: {
    ...Typography.TEXT_STYLES.body,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
  },
  scrollView: {
    flex: 1,
  },
  errorCard: {
    marginBottom: SPACING.lg,
    backgroundColor: "#FFEBEE",
    borderColor: Colors.DANGER,
  },
  errorText: {
    color: Colors.DANGER,
    ...Typography.TEXT_STYLES.body,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    ...Typography.TEXT_STYLES.body,
    color: Colors.WHITE,
    marginBottom: SPACING.sm,
    fontWeight: "600",
  },
  input: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  signUpButton: {
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: SPACING.lg,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  dividerText: {
    ...Typography.TEXT_STYLES.body,
    color: "rgba(255, 255, 255, 0.8)",
    paddingHorizontal: SPACING.md,
  },
  googleButton: {
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 12,
    padding: SPACING.md,
    alignItems: "center",
    marginBottom: SPACING.lg,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: SPACING.sm,
  },
  googleButtonText: {
    ...Typography.TEXT_STYLES.body,
    color: Colors.WHITE,
    fontWeight: "600",
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  loginText: {
    ...Typography.TEXT_STYLES.body,
    color: "rgba(255, 255, 255, 0.8)",
    marginRight: SPACING.xs,
  },
  loginLink: {
    ...Typography.TEXT_STYLES.body,
    color: Colors.WHITE,
    fontWeight: "bold",
    textDecorationLine: "underline",
  },
});

export default SignUpScreen;
