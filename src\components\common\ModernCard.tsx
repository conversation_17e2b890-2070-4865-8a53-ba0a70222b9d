import React from 'react';
import { View, ViewStyle, TouchableOpacity } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { BORDER_RADIUS, SPACING, SHADOW, CARD_PADDING } from '../../theme/spacing';

interface ModernCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'small' | 'medium' | 'large' | 'xl';
}

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  style,
  onPress,
  variant = 'default',
  padding = 'medium',
  borderRadius = 'medium',
}) => {
  const { colors } = useTheme();

  const getPaddingStyles = () => {
    switch (padding) {
      case 'none':
        return {};
      case 'small':
        return {
          paddingHorizontal: SPACING.sm,
          paddingVertical: SPACING.sm,
        };
      case 'large':
        return {
          paddingHorizontal: SPACING.xl,
          paddingVertical: SPACING.lg,
        };
      default:
        return {
          paddingHorizontal: CARD_PADDING.horizontal,
          paddingVertical: CARD_PADDING.vertical,
        };
    }
  };

  const getBorderRadiusValue = () => {
    switch (borderRadius) {
      case 'small':
        return BORDER_RADIUS.sm;
      case 'large':
        return BORDER_RADIUS.lg;
      case 'xl':
        return BORDER_RADIUS.xl;
      default:
        return BORDER_RADIUS.md;
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: colors.CARD_BACKGROUND,
          ...SHADOW.lg,
        };
      case 'outlined':
        return {
          backgroundColor: colors.CARD_BACKGROUND,
          borderWidth: 1,
          borderColor: colors.BORDER,
        };
      case 'flat':
        return {
          backgroundColor: 'transparent',
        };
      default:
        return {
          backgroundColor: colors.CARD_BACKGROUND,
          ...SHADOW.sm,
        };
    }
  };

  const cardStyle: ViewStyle = {
    borderRadius: getBorderRadiusValue(),
    ...getVariantStyles(),
    ...getPaddingStyles(),
    ...style,
  };

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={cardStyle}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};
