import React from "react";
import {
  createStackNavigator,
  TransitionPresets,
} from "@react-navigation/stack";
import BottomTabNavigator from "./BottomTabNavigator";
import {
  AddRoomScreen,
  RoomDetails,
  MotelsManagement,
  AddEditMotel,
  UpdatePersonalInfo,
} from "../screens";
import BillScreen from "../screens/room/BillScreen";
import EditHistoryScreen from "../screens/profile/EditHistoryScreen";
import { AppStackParamList } from "../types";

const AppStack = createStackNavigator<AppStackParamList>();

const AppNavigator = () => {
  return (
    <AppStack.Navigator
      screenOptions={{
        headerShown: false,
        ...TransitionPresets.SlideFromRightIOS,
        animationEnabled: true,
        gestureEnabled: true,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <AppStack.Screen name="MainTabs" component={BottomTabNavigator} />
      <AppStack.Screen name="AddRoom" component={AddRoomScreen} />
      <AppStack.Screen name="RoomDetails" component={RoomDetails} />
      <AppStack.Screen name="MotelsManagement" component={MotelsManagement} />
      <AppStack.Screen
        name="AddEditMotel"
        component={AddEditMotel}
        options={{
          ...TransitionPresets.ModalSlideFromBottomIOS,
        }}
      />
      <AppStack.Screen
        name="UpdatePersonalInfo"
        component={UpdatePersonalInfo}
      />
      <AppStack.Screen name="BillScreen" component={BillScreen} />
      <AppStack.Screen name="EditHistoryScreen" component={EditHistoryScreen} />
    </AppStack.Navigator>
  );
};

export default AppNavigator;
