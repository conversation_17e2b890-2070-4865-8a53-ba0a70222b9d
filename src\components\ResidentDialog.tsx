import React, { useState, useEffect } from "react";
import {
  Dialog,
  Portal,
  Text,
  TextInput,
  Button,
  Switch,
  HelperText,
  Divider,
} from "react-native-paper";
import { View, StyleSheet } from "react-native";
import { useTheme } from "../context/ThemeContext";
import { useLanguage } from "../context/LanguageContext";
import { Resident } from "../types";

interface ResidentDialogProps {
  visible: boolean;
  onDismiss: () => void;
  onSave: (resident: Partial<Resident>) => void;
  resident?: Resident | null;
  mode: "add" | "edit";
}

export default function ResidentDialog({
  visible,
  onDismiss,
  onSave,
  resident,
  mode,
}: ResidentDialogProps) {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();

  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    identity_number: "",
    is_main_tenant: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (mode === "edit" && resident) {
      setFormData({
        name: resident.name,
        phone: resident.phone,
        identity_number: resident.identity_number,
        is_main_tenant: resident.is_main_tenant,
      });
    } else {
      setFormData({
        name: "",
        phone: "",
        identity_number: "",
        is_main_tenant: false,
      });
    }
    setErrors({});
  }, [mode, resident, visible]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t("validation.required");
    }

    if (!formData.phone.trim()) {
      newErrors.phone = t("validation.required");
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = t("validation.invalidPhone");
    }

    if (!formData.identity_number.trim()) {
      newErrors.identity_number = t("validation.required");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    const residentData: Partial<Resident> = {
      ...formData,
      ...(mode === "edit" && resident ? { resident_id: resident.resident_id } : {}),
    };

    onSave(residentData);
    onDismiss();
  };

  const handleCancel = () => {
    setFormData({
      name: "",
      phone: "",
      identity_number: "",
      is_main_tenant: false,
    });
    setErrors({});
    onDismiss();
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={handleCancel}
        style={[
          styles.dialog,
          { backgroundColor: isDarkMode ? colors.CARD_BACKGROUND : colors.WHITE },
        ]}
      >
        <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
          {mode === "edit" ? t("resident.editResident") : t("resident.addResident")}
        </Dialog.Title>
        
        <Divider style={{ backgroundColor: colors.DIVIDER }} />
        
        <Dialog.Content style={styles.content}>
          <TextInput
            mode="outlined"
            label={t("resident.name")}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            style={[styles.input, { backgroundColor: colors.CARD_BACKGROUND }]}
            error={!!errors.name}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.name && <HelperText type="error">{errors.name}</HelperText>}

          <TextInput
            mode="outlined"
            label={t("resident.phone")}
            value={formData.phone}
            onChangeText={(text) => setFormData({ ...formData, phone: text })}
            style={[styles.input, { backgroundColor: colors.CARD_BACKGROUND }]}
            keyboardType="phone-pad"
            error={!!errors.phone}
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone && <HelperText type="error">{errors.phone}</HelperText>}

          <TextInput
            mode="outlined"
            label={t("resident.identityNumber")}
            value={formData.identity_number}
            onChangeText={(text) => setFormData({ ...formData, identity_number: text })}
            style={[styles.input, { backgroundColor: colors.CARD_BACKGROUND }]}
            error={!!errors.identity_number}
            left={<TextInput.Icon icon="card-account-details" />}
          />
          {errors.identity_number && (
            <HelperText type="error">{errors.identity_number}</HelperText>
          )}

          <View style={styles.switchContainer}>
            <Text style={[styles.switchLabel, { color: colors.TEXT_PRIMARY }]}>
              {t("resident.isMainTenant")}
            </Text>
            <Switch
              value={formData.is_main_tenant}
              onValueChange={(value) =>
                setFormData({ ...formData, is_main_tenant: value })
              }
              color="#70C4D7"
            />
          </View>
        </Dialog.Content>

        <Dialog.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={handleCancel}
            textColor={colors.TEXT_SECONDARY}
          >
            {t("common.cancel")}
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            buttonColor="#70C4D7"
            textColor={colors.WHITE}
          >
            {mode === "edit" ? t("common.update") : t("common.add")}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}

const styles = StyleSheet.create({
  dialog: {
    marginHorizontal: 20,
    borderRadius: 12,
  },
  content: {
    paddingVertical: 20,
  },
  input: {
    marginBottom: 8,
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  actions: {
    paddingHorizontal: 24,
    paddingBottom: 20,
    gap: 8,
  },
});
