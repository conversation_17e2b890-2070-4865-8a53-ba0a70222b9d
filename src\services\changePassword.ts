import { auth } from '../../config/firebase';
import { reauthenticateWithCredential, EmailAuthProvider, updatePassword } from 'firebase/auth';

export const changeUserPassword = async (currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
  if (!auth.currentUser || !auth.currentUser.email) {
    return { success: false, message: 'User not authenticated' };
  }
  try {
    const credential = EmailAuthProvider.credential(auth.currentUser.email, currentPassword);
    await reauthenticateWithCredential(auth.currentUser, credential);
    await updatePassword(auth.currentUser, newPassword);
    return { success: true, message: 'Password updated successfully' };
  } catch (error: any) {
    let message = 'Có lỗi xảy ra';
    if (error.code === 'auth/wrong-password') message = 'Mật khẩu hiện tại không đúng';
    else if (error.code === 'auth/weak-password') message = '<PERSON><PERSON><PERSON> khẩu mới quá yếu';
    else if (error.code === 'auth/too-many-requests') message = '<PERSON><PERSON><PERSON> đã thử quá nhiều lần. Vui lòng thử lại sau.';
    return { success: false, message };
  }
};
