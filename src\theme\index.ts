// Export all theme constants
import * as Colors from "./colors";
import * as Spacing from "./spacing";
import * as Typography from "./typography";
import { MD3LightTheme, MD3DarkTheme } from "react-native-paper";

// Create light and dark themes for react-native-paper
export const createPaperTheme = (isDark: boolean = false) => {
  const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
  const themeColors = isDark ? Colors.DARK : Colors.LIGHT;

  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: Colors.PRIMARY,
      secondary: Colors.SECONDARY,
      accent: Colors.ACCENT,
      background: themeColors.BACKGROUND,
      surface: themeColors.SURFACE,
      error: Colors.DANGER,
      text: themeColors.TEXT_PRIMARY,
      onSurface: themeColors.TEXT_PRIMARY,
      disabled: themeColors.GRAY_DARK,
      placeholder: themeColors.TEXT_HINT,
      backdrop: themeColors.BLACK + "80", // 50% opacity
      notification: Colors.PRIMARY,
    },
    // Keep for backward compatibility
    dark: isDark,
  };
};

// For backward compatibility
export const paperTheme = createPaperTheme(false);

export { Colors, Spacing, Typography };
