import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import {
  Text,
  Surface,
  Chip,
  Divider,
  Button,
  Portal,
  Dialog,
  List,
} from "react-native-paper";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppStackParamList } from "../../types";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { Line<PERSON><PERSON>, PieChart } from "react-native-chart-kit";
import { Dimensions } from "react-native";
import {
  getTotalRevenue,
  getTotalUnpaid,
  getBillsByRoomId,
  getMonthlyRevenue,
  getAverageRevenueByMonth,
  getTotalUnpaidByMonth,
  getOnTimePaymentRateByMonth,
  getRentalRateByMonth,
  getRevenueGrowthRate,
} from "../../services/BillServices";
import { useMotel } from "../../context/MotelContext";
import { subscribeToRooms } from "../../services/roomService";
import { Room } from "../../types";
import { getTenantsByMotelId } from "../../services/TenantServices";
import { getRoomStatsByMotel } from "../../services/roomService";
import { Platform } from "react-native";

const screenWidth = Dimensions.get("window").width;

const ReportScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackParamList>>();
  const { t } = useLanguage();
  const { isDarkMode, colors } = useTheme();
  const [activeTab, setActiveTab] = useState("operations"); // operations, finance
  const [operationsSubTab, setOperationsSubTab] = useState("room"); // room, building
  const [financeSubTab, setFinanceSubTab] = useState("revenue"); // revenue, expenses
  const [roomFilter, setRoomFilter] = useState("all"); // all, overdue, paid, repair, empty
  const { motelId } = useMotel();
  const [revenueAmount, setRevenueAmount] = useState<number>(0);
  const [unpaidAmount, setUnpaidAmount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [tenants, setTenants] = useState<any[]>([]);
  const [roomStats, setRoomStats] = useState({
    total: 0,
    available: 0,
    occupied: 0,
    maintenance: 0,
  });
  const [revenueMonths, setRevenueMonths] = useState<number>(3); // 3, 6, 12
  const [monthlyRevenue, setMonthlyRevenue] = useState<
    { label: string; value: number }[]
  >([]);
  const [monthPickerVisible, setMonthPickerVisible] = useState(false);

  // Biến tháng hiện tại (ví dụ: 2025-06)
  const currentMonth = `${new Date().getFullYear()}-${String(
    new Date().getMonth() + 1
  ).padStart(2, "0")}`;
  // State lưu trạng thái bill của từng phòng cho tháng hiện tại
  const [roomBillStatus, setRoomBillStatus] = useState<
    Record<string, "overdue" | "paid" | "pending" | undefined>
  >({});
  // Thêm state chọn tháng/năm
  const [selectedMonth, setSelectedMonth] = useState<{
    month: number;
    year: number;
  }>(() => {
    const now = new Date();
    return { month: now.getMonth() + 1, year: now.getFullYear() };
  });

  // State for monthly statistics
  const [averageRevenue, setAverageRevenue] = useState<number>(0);
  const [monthlyUnpaid, setMonthlyUnpaid] = useState<number>(0);
  const [onTimeRate, setOnTimeRate] = useState<number>(0);
  const [rentalRate, setRentalRate] = useState<number>(0);
  const [growthRate, setGrowthRate] = useState<number>(0);

  // State for previous month statistics
  const [prevAverageRevenue, setPrevAverageRevenue] = useState<number>(0);
  const [prevMonthlyUnpaid, setPrevMonthlyUnpaid] = useState<number>(0);
  const [prevOnTimeRate, setPrevOnTimeRate] = useState<number>(0);
  const [prevRentalRate, setPrevRentalRate] = useState<number>(0);

  const roomOccupancyData = {
    labels: ["09 May", "10 May", "11 May", "12 May", "13 May", "14 May"],
    datasets: [
      {
        data: [42, 35, 28, 32, 28, 25],
        color: () => "#70C4D7", // Primary color
        strokeWidth: 2,
      },
    ],
  };

  const revenueData = {
    labels: monthlyRevenue.map((item) => item.label),
    datasets: [
      {
        data: monthlyRevenue.map((item) => item.value), // dữ liệu gốc (VND)
        color: () => "#70C4D7", // Primary color
        strokeWidth: 2,
      },
    ],
  };

  const expensesData = [
    {
      name: t("report.staffSalary"),
      population: 12,
      color: "#4285F4",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.maintenance"),
      population: 7,
      color: "#F4B400",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.renovation"),
      population: 6,
      color: "#0F9D58",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
  ];

  const chartConfig = {
    backgroundGradientFrom: isDarkMode ? colors.CARD_BACKGROUND : "#fff",
    backgroundGradientTo: isDarkMode ? colors.CARD_BACKGROUND : "#fff",
    decimalPlaces: 0,
    color: () => (isDarkMode ? colors.TEXT_PRIMARY : "#333"),
    labelColor: () => (isDarkMode ? colors.TEXT_SECONDARY : "#999"),
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: "6",
      strokeWidth: "2",
      stroke: "#70C4D7",
    },
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <MaterialCommunityIcons
          name="chevron-left"
          size={30}
          color={colors.WHITE}
        />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{t("report.statistics")}</Text>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "operations" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("operations")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "operations" && styles.activeTabText,
            ]}
          >
            {t("report.operations")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "finance" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("finance")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "finance" && styles.activeTabText,
            ]}
          >
            {t("report.finance")}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderOperationsSubTabs = () => (
    <View style={styles.subTabContainer}>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          operationsSubTab === "room" && styles.activeSubTabButton,
        ]}
        onPress={() => setOperationsSubTab("room")}
      >
        <Text
          style={[
            styles.subTabText,
            operationsSubTab === "room" && styles.activeSubTabText,
          ]}
        >
          {t("report.room")}
        </Text>
        {operationsSubTab === "room" && <View style={styles.subTabIndicator} />}
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          operationsSubTab === "building" && styles.activeSubTabButton,
        ]}
        onPress={() => setOperationsSubTab("building")}
      >
        <Text
          style={[
            styles.subTabText,
            operationsSubTab === "building" && styles.activeSubTabText,
          ]}
        >
          {t("report.building")}
        </Text>
        {operationsSubTab === "building" && (
          <View style={styles.subTabIndicator} />
        )}
      </TouchableOpacity>
    </View>
  );

  const renderFinanceSubTabs = () => (
    <View style={styles.subTabContainer}>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          financeSubTab === "revenue" && styles.activeSubTabButton,
        ]}
        onPress={() => setFinanceSubTab("revenue")}
      >
        <Text
          style={[
            styles.subTabText,
            financeSubTab === "revenue" && styles.activeSubTabText,
          ]}
        >
          {t("report.revenue")}
        </Text>
        {financeSubTab === "revenue" && <View style={styles.subTabIndicator} />}
      </TouchableOpacity>
    </View>
  );

  const renderMonthPicker = () => {
    // Tạo danh sách 12 tháng gần nhất
    const now = new Date();
    const months = Array.from({ length: 12 }, (_, i) => {
      const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
      return { month: d.getMonth() + 1, year: d.getFullYear() };
    });
    const currentLabel = `${selectedMonth.month.toString().padStart(2, "0")}/${
      selectedMonth.year
    }`;
    return (
      <View
        style={{ flexDirection: "row", alignItems: "center", marginBottom: 12 }}
      >
        <MaterialCommunityIcons
          name="calendar-month"
          size={22}
          color={colors.TEXT_PRIMARY}
          style={{ marginRight: 8 }}
        />
        <Text style={{ color: colors.TEXT_PRIMARY, marginRight: 8 }}>
          {t("report.selectMonth") || "Chọn tháng"}
        </Text>
        <TouchableOpacity
          onPress={() => setMonthPickerVisible(true)}
          style={{
            backgroundColor: "#F5F5F5",
            borderRadius: 16,
            paddingHorizontal: 16,
            paddingVertical: 8,
          }}
        >
          <Text style={{ color: colors.TEXT_PRIMARY, fontWeight: "bold" }}>
            {currentLabel}
          </Text>
        </TouchableOpacity>
        <Portal>
          <Dialog
            visible={monthPickerVisible}
            onDismiss={() => setMonthPickerVisible(false)}
          >
            <Dialog.Title>
              {t("report.selectMonth") || "Chọn tháng"}
            </Dialog.Title>
            <Dialog.ScrollArea>
              <ScrollView style={{ maxHeight: 300 }}>
                {months.map(({ month, year }) => {
                  const label = `${month.toString().padStart(2, "0")}/${year}`;
                  return (
                    <List.Item
                      key={label}
                      title={label}
                      onPress={() => {
                        setSelectedMonth({ month, year });
                        setMonthPickerVisible(false);
                      }}
                      left={(props) =>
                        selectedMonth.month === month &&
                        selectedMonth.year === year ? (
                          <List.Icon {...props} icon="check" color="#70C4D7" />
                        ) : null
                      }
                    />
                  );
                })}
              </ScrollView>
            </Dialog.ScrollArea>
          </Dialog>
        </Portal>
      </View>
    );
  };

  const renderRoomManagement = () => {
    // Tính số phòng theo filter
    const filteredRooms = rooms.filter((room) => {
      if (roomFilter === "all") return true;
      if (roomFilter === "overdue")
        return roomBillStatus[room.room_id] === "overdue";
      if (roomFilter === "paid") return roomBillStatus[room.room_id] === "paid";
      if (roomFilter === "repair") return room.status === "maintenance";
      if (roomFilter === "empty") return room.status === "available";
      return true;
    });
    return (
      <ScrollView style={styles.container}>
        {/* XÓA khung chứa giai đoạn, cập nhật, ngày */}
        {/* <Surface
          style={[
            styles.dateRangeCard,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={1}
        >
          <Text style={{ color: colors.TEXT_PRIMARY }}>
            {t("report.datePeriod")} 01/04 - 24/04
          </Text>
          <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
            {t("report.lastUpdated")} 20:25
          </Text>
          <View style={styles.datePickerButton}>
            <Text style={{ color: colors.TEXT_PRIMARY }}>{t("report.date")}</Text>
            <MaterialCommunityIcons
              name="chevron-down"
              size={20}
              color={colors.TEXT_PRIMARY}
            />
          </View>
        </Surface> */}

        <Surface
          style={[
            styles.sectionCard,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={1}
        >
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.roomManagement")}
          </Text>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.statusFiltersContainer}
          >
            <View style={styles.statusFilters}>
              <Chip
                selected={roomFilter === "all"}
                onPress={() => setRoomFilter("all")}
                style={[
                  styles.statusFilter,
                  roomFilter === "all" && { backgroundColor: "#70C4D7" },
                ]}
                textStyle={[
                  {
                    color:
                      roomFilter === "all" ? colors.WHITE : colors.TEXT_PRIMARY,
                  },
                ]}
              >
                {t("report.all")}
              </Chip>
              <Chip
                selected={roomFilter === "overdue"}
                onPress={() => setRoomFilter("overdue")}
                style={[
                  styles.statusFilter,
                  roomFilter === "overdue" && { backgroundColor: "#70C4D7" },
                ]}
                textStyle={[
                  {
                    color:
                      roomFilter === "overdue"
                        ? colors.WHITE
                        : colors.TEXT_PRIMARY,
                  },
                ]}
              >
                {t("report.unpaid") || "Chưa thanh toán"}
              </Chip>
              <Chip
                selected={roomFilter === "paid"}
                onPress={() => {
                  setRoomFilter("paid");
                  console.log(
                    'roomBillStatus khi chọn filter "paid":',
                    roomBillStatus
                  );
                }}
                style={[
                  styles.statusFilter,
                  roomFilter === "paid" && { backgroundColor: "#70C4D7" },
                ]}
                textStyle={[
                  {
                    color:
                      roomFilter === "paid"
                        ? colors.WHITE
                        : colors.TEXT_PRIMARY,
                  },
                ]}
              >
                {t("report.paid")}
              </Chip>
              <Chip
                selected={roomFilter === "repair"}
                onPress={() => setRoomFilter("repair")}
                style={[
                  styles.statusFilter,
                  roomFilter === "repair" && { backgroundColor: "#70C4D7" },
                ]}
                textStyle={[
                  {
                    color:
                      roomFilter === "repair"
                        ? colors.WHITE
                        : colors.TEXT_PRIMARY,
                  },
                ]}
              >
                {t("report.repair")}
              </Chip>
              <Chip
                selected={roomFilter === "empty"}
                onPress={() => setRoomFilter("empty")}
                style={[
                  styles.statusFilter,
                  roomFilter === "empty" && { backgroundColor: "#70C4D7" },
                ]}
                textStyle={[
                  {
                    color:
                      roomFilter === "empty"
                        ? colors.WHITE
                        : colors.TEXT_PRIMARY,
                  },
                ]}
              >
                {t("report.empty")}
              </Chip>
            </View>
          </ScrollView>

          <View style={styles.roomCountContainer}>
            <MaterialCommunityIcons name="door" size={24} color="#70C4D7" />
            <Text style={[styles.roomCount, { color: colors.TEXT_PRIMARY }]}>
              {filteredRooms.length}
            </Text>
            <Text
              style={[styles.roomCountLabel, { color: colors.TEXT_SECONDARY }]}
            >
              {t("report.rooms")}
            </Text>
            {/* <Button
              mode="contained"
              style={styles.truyVanButton}
              labelStyle={styles.truyVanButtonText}
            >
              {t("report.query")}
            </Button> */}
          </View>

          {/* Danh sách phòng không chia tầng - render động */}
          {/* Danh sách phòng chia theo dãy */}
          {Object.keys(groupedRooms)
            .sort()
            .map((prefix) => {
              const filtered = groupedRooms[prefix].filter((room) => {
                if (roomFilter === "all") return true;
                if (roomFilter === "overdue")
                  return roomBillStatus[room.room_id] === "overdue";
                if (roomFilter === "paid")
                  return roomBillStatus[room.room_id] === "paid";
                if (roomFilter === "repair")
                  return room.status === "maintenance";
                if (roomFilter === "empty") return room.status === "available";
                return true;
              });
              if (filtered.length === 0) return null;
              return (
                <View key={prefix} style={{ marginBottom: 16 }}>
                  <Text
                    style={[
                      styles.floorTitle,
                      { color: colors.TEXT_PRIMARY, marginBottom: 8 },
                    ]}
                  >
                    Dãy {prefix}
                  </Text>
                  <View style={styles.roomGrid}>
                    {filtered.map((room) => {
                      // Xác định màu theo trạng thái bill tháng hiện tại
                      let bgColor = "#000000";
                      if (roomBillStatus[room.room_id] === "overdue")
                        bgColor = "#FF5722"; // đỏ
                      else if (roomBillStatus[room.room_id] === "pending")
                        bgColor = "#FFC107"; // vàng
                      else if (roomBillStatus[room.room_id] === "paid")
                        bgColor = "#4CAF50"; // xanh lá
                      else if (room.status === "occupied")
                        bgColor = "#70C4D7"; // xanh biển
                      else if (room.status === "maintenance")
                        bgColor = "#CCCCCC";
                      else if (room.status === "available") bgColor = "#808080"; // xám
                      return (
                        <TouchableOpacity
                          key={room.room_id}
                          style={[
                            styles.roomItem,
                            { backgroundColor: bgColor },
                          ]}
                          onPress={() =>
                            navigation.navigate("RoomDetails", {
                              roomId: room.room_id,
                              roomNumber: room.room_number,
                              motelId: room.motel_id,
                              initialStatus: room.status,
                            })
                          }
                        >
                          <Text style={styles.roomNumber}>
                            {room.room_number}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}
                  </View>
                </View>
              );
            })}
        </Surface>

        <Surface
          style={[
            styles.sectionCard,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={1}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons
              name="account-group"
              size={24}
              color="#70C4D7"
            />
            <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.tenantManagement")}
            </Text>
          </View>
          <Text
            style={[styles.tenantListLabel, { color: colors.TEXT_SECONDARY }]}
          >
            {t("report.tenantInfo")}
          </Text>

          <View style={styles.tenantList}>
            {getTop3Tenants().map((tenant, idx) => (
              <View key={tenant.resident_id || idx} style={styles.tenantItem}>
                <Text
                  style={[styles.tenantRoom, { color: colors.TEXT_PRIMARY }]}
                >
                  {tenant.room_number} - {tenant.name}
                </Text>
                <Text
                  style={[
                    styles.tenantStatus,
                    {
                      color:
                        tenant.status === "overdue"
                          ? "#FF5722"
                          : tenant.status === "expiring"
                          ? "#FFC107"
                          : "#4CAF50",
                    },
                  ]}
                >
                  {tenant.status === "overdue"
                    ? "Đ đóng trọ"
                    : tenant.status === "expiring"
                    ? "Sắp đến hạn"
                    : "Đã đóng trọ"}
                </Text>
              </View>
            ))}
          </View>
        </Surface>
      </ScrollView>
    );
  };

  const renderBuildingTab = () => (
    <ScrollView style={styles.container}>
      <Surface
        style={[
          styles.overviewCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.overviewHeader}>
          <MaterialCommunityIcons name="chart-bar" size={24} color="#70C4D7" />
          <Text style={[styles.overviewTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.overview")}
          </Text>
        </View>

        {/* Tổng quan phòng - 4 trạng thái */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginBottom: 24,
          }}
        >
          <View style={{ alignItems: "center", flex: 1 }}>
            <MaterialCommunityIcons
              name="home-group"
              size={28}
              color="#1976D2"
            />
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                fontWeight: "bold",
                fontSize: 20,
              }}
            >
              {roomStats.total}
            </Text>
            <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
              {t("report.totalRooms")}
            </Text>
          </View>
          <View style={{ alignItems: "center", flex: 1 }}>
            <MaterialCommunityIcons
              name="account-key"
              size={28}
              color="#70C4D7"
            />
            <Text
              style={{ color: "#70C4D7", fontWeight: "bold", fontSize: 20 }}
            >
              {roomStats.occupied}
            </Text>
            <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
              {t("report.rented")}
            </Text>
          </View>
          <View style={{ alignItems: "center", flex: 1 }}>
            <MaterialCommunityIcons
              name="door-open"
              size={28}
              color="#FFC107"
            />
            <Text
              style={{ color: "#FFC107", fontWeight: "bold", fontSize: 20 }}
            >
              {roomStats.available}
            </Text>
            <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
              {t("report.empty")}
            </Text>
          </View>
          <View style={{ alignItems: "center", flex: 1 }}>
            <MaterialCommunityIcons name="tools" size={28} color="#4CAF50" />
            <Text
              style={{ color: "#4CAF50", fontWeight: "bold", fontSize: 20 }}
            >
              {roomStats.maintenance}
            </Text>
            <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
              {t("report.underRepair")}
            </Text>
          </View>
        </View>

        <Text style={[styles.chartTitle, { color: colors.TEXT_PRIMARY }]}>
          {t("report.chart")}
        </Text>
        <PieChart
          data={pieData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="85"
          absolute
          hasLegend={false}
        />
        <View style={styles.legendContainer}>
          {pieData.map((item, idx) => (
            <View key={item.name} style={styles.legendItem}>
              <View
                style={[styles.legendColor, { backgroundColor: item.color }]}
              />
              <Text style={{ color: colors.TEXT_PRIMARY }}>{item.name}</Text>
              <Text style={{ color: colors.TEXT_PRIMARY, marginLeft: "auto" }}>
                {item.population}
              </Text>
              <Text
                style={{
                  color: colors.TEXT_PRIMARY,
                  width: 50,
                  textAlign: "right",
                }}
              >
                {roomStats.total > 0
                  ? ((item.population / roomStats.total) * 100).toFixed(1)
                  : 0}
                {t("report.percentValue")}
              </Text>
            </View>
          ))}
        </View>
      </Surface>
    </ScrollView>
  );

  const renderRevenueTab = () => (
    <ScrollView style={styles.container}>
      {renderMonthPicker()}
      <Surface
        style={[
          styles.revenueCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.revenueHeader}>
          <View style={styles.revenueHeaderLeft}>
            <MaterialCommunityIcons
              name="cash-multiple"
              size={24}
              color="#70C4D7"
            />
            <Text style={[styles.revenueTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.totalRevenue")}
            </Text>
          </View>
          <MaterialCommunityIcons
            name="chevron-right"
            size={24}
            color={colors.TEXT_PRIMARY}
          />
        </View>
        <Text style={[styles.revenueAmount, { color: "#4CAF50" }]}>
          {revenueAmount.toLocaleString()} VND
        </Text>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "flex-start",
            marginTop: 0,
            marginBottom: 8,
          }}
        >
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name={growthRate >= 0 ? "arrow-up" : "arrow-down"}
              size={16}
              color={growthRate >= 0 ? "#4CAF50" : "#FF5722"}
            />
            <Text
              style={{
                color: growthRate >= 0 ? "#4CAF50" : "#FF5722",
                marginLeft: 4,
              }}
            >
              {growthRate}%
            </Text>
          </View>
        </View>
        {/* <Text style={[styles.revenuePeriod, { color: colors.TEXT_SECONDARY }]}> 
          {t('report.revenue')} {selectedMonth.month.toString().padStart(2, '0')}/{selectedMonth.year}
        </Text> */}
      </Surface>

      <View style={styles.revenueGridContainer}>
        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>{t("report.revenue")} TB</Text>
          <Text style={styles.revenueGridValue}>
            {averageRevenue.toLocaleString()} VND
          </Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name={
                averageRevenue - prevAverageRevenue >= 0
                  ? "arrow-up"
                  : "arrow-down"
              }
              size={16}
              color={
                averageRevenue - prevAverageRevenue >= 0 ? "#FFFFFF" : "#FF5722"
              }
            />
            <Text
              style={{
                color:
                  averageRevenue - prevAverageRevenue >= 0
                    ? "#FFFFFF"
                    : "#FF5722",
              }}
            >
              {prevAverageRevenue !== 0
                ? (
                    ((averageRevenue - prevAverageRevenue) /
                      Math.abs(prevAverageRevenue)) *
                    100
                  ).toFixed(1)
                : "-"}
              %
            </Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#333333" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>
            {t("report.unpaid") || "Chưa TT"}
          </Text>
          <Text style={styles.revenueGridValue}>
            {monthlyUnpaid.toLocaleString()} VND
          </Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name={
                monthlyUnpaid - prevMonthlyUnpaid >= 0
                  ? "arrow-up"
                  : "arrow-down"
              }
              size={16}
              color={
                monthlyUnpaid - prevMonthlyUnpaid >= 0 ? "#FF5722" : "#4CAF50"
              }
            />
            <Text
              style={{
                color:
                  monthlyUnpaid - prevMonthlyUnpaid >= 0
                    ? "#FF5722"
                    : "#4CAF50",
              }}
            >
              {prevMonthlyUnpaid !== 0
                ? (
                    ((monthlyUnpaid - prevMonthlyUnpaid) /
                      Math.abs(prevMonthlyUnpaid)) *
                    100
                  ).toFixed(1)
                : "-"}
              %
            </Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>
            {t("report.onTimePayment")}
          </Text>
          <Text style={styles.revenueGridValue}>{onTimeRate}%</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name={
                onTimeRate - prevOnTimeRate >= 0 ? "arrow-up" : "arrow-down"
              }
              size={16}
              color={onTimeRate - prevOnTimeRate >= 0 ? "#FFFFFF" : "#FF5722"}
            />
            <Text
              style={{
                color: onTimeRate - prevOnTimeRate >= 0 ? "#FFFFFF" : "#FF5722",
              }}
            >
              {prevOnTimeRate !== 0
                ? (onTimeRate - prevOnTimeRate).toFixed(1)
                : "-"}
              %
            </Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>{t("report.rentalRate")}</Text>
          <Text style={styles.revenueGridValue}>{rentalRate}%</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name={
                rentalRate - prevRentalRate >= 0 ? "arrow-up" : "arrow-down"
              }
              size={16}
              color={rentalRate - prevRentalRate >= 0 ? "#FFFFFF" : "#FF5722"}
            />
            <Text
              style={{
                color: rentalRate - prevRentalRate >= 0 ? "#FFFFFF" : "#FF5722",
              }}
            >
              {prevRentalRate !== 0
                ? (rentalRate - prevRentalRate).toFixed(1)
                : "-"}
              %
            </Text>
          </View>
        </Surface>
      </View>

      <Surface
        style={[styles.chartCard, { backgroundColor: colors.CARD_BACKGROUND }]}
        elevation={1}
      >
        <Text style={[styles.chartTitle, { color: colors.TEXT_PRIMARY }]}>
          {t("report.chart")}
        </Text>
        <View style={styles.chartFilterContainer}>
          <TouchableOpacity
            onPress={() => setRevenueMonths(3)}
            style={{ marginHorizontal: 4 }}
          >
            <Text
              style={{
                color: revenueMonths === 3 ? "#70C4D7" : colors.TEXT_PRIMARY,
              }}
            >
              3 tháng
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setRevenueMonths(6)}
            style={{ marginHorizontal: 4 }}
          >
            <Text
              style={{
                color: revenueMonths === 6 ? "#70C4D7" : colors.TEXT_PRIMARY,
              }}
            >
              6 tháng
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setRevenueMonths(12)}
            style={{ marginHorizontal: 4 }}
          >
            <Text
              style={{
                color: revenueMonths === 12 ? "#70C4D7" : colors.TEXT_PRIMARY,
              }}
            >
              12 tháng
            </Text>
          </TouchableOpacity>
        </View>
        <LineChart
          data={revenueData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withInnerLines={true}
          withOuterLines={true}
          withDots={true}
          withShadow={true}
          fromZero={true}
          formatXLabel={(label) => label.split("/")[0]}
          formatYLabel={(y) => formatTrieu(Number(y))}
        />
      </Surface>
    </ScrollView>
  );

  const renderFinanceTab = () => {
    return (
      <View style={styles.tabContent}>
        {renderFinanceSubTabs()}
        {renderRevenueTab()}
      </View>
    );
  };

  const renderOperationsTab = () => {
    return (
      <View style={styles.tabContent}>
        {renderOperationsSubTabs()}
        {operationsSubTab === "room"
          ? renderRoomManagement()
          : renderBuildingTab()}
      </View>
    );
  };

  // Nhóm phòng theo tiền tố (dãy) và sắp xếp theo số thứ tự
  const groupedRooms = rooms.reduce((acc, room) => {
    const prefix = room.room_number ? room.room_number[0] : "?";
    if (!acc[prefix]) acc[prefix] = [];
    acc[prefix].push(room);
    return acc;
  }, {} as Record<string, Room[]>);
  // Sắp xếp từng dãy theo số thứ tự
  Object.keys(groupedRooms).forEach((prefix) => {
    groupedRooms[prefix].sort((a, b) => {
      const aNum = parseInt(a.room_number?.slice(1) || "0", 10);
      const bNum = parseInt(b.room_number?.slice(1) || "0", 10);
      return aNum - bNum;
    });
  });

  // Format số tiền kiểu 2Tr564k
  const formatTrieu = (amount: number) => {
    if (amount == null || isNaN(amount)) return "-";
    const trieu = Math.floor(amount / 1_000_000);
    const ngan = Math.round((amount % 1_000_000) / 1000);
    if (trieu > 0 && ngan > 0) return `${trieu}Tr${ngan}k`;
    if (trieu > 0) return `${trieu}Tr`;
    if (ngan > 0) return `${ngan}k`;
    return "0";
  };

  // Optimized data loading with parallel requests
  const loadInitialData = useCallback(async () => {
    if (!motelId) return;

    setIsLoading(true);
    try {
      // Load data in parallel for better performance
      const [unpaidAmount, roomStats, tenants] = await Promise.all([
        getTotalUnpaid(motelId),
        getRoomStatsByMotel(motelId),
        getTenantsByMotelId(motelId),
      ]);

      setUnpaidAmount(unpaidAmount);
      setRoomStats(roomStats);
      setTenants(tenants);
    } catch (error) {
      console.error("Error loading initial data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [motelId]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  useEffect(() => {
    if (motelId) {
      const unsubscribe = subscribeToRooms(setRooms, motelId);
      return unsubscribe;
    }
  }, [motelId]);

  // Khi rooms thay đổi, lấy trạng thái bill tháng hiện tại cho từng phòng
  useEffect(() => {
    if (rooms.length === 0) return;
    const fetchBills = async () => {
      // Lấy ngày hiện tại (yyyy-mm-dd)
      const currentDate = new Date();
      const todayStr = `${currentDate.getFullYear()}-${String(
        currentDate.getMonth() + 1
      ).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`;
      const statusMap: Record<
        string,
        "overdue" | "paid" | "pending" | undefined
      > = {};
      await Promise.all(
        rooms.map(async (room) => {
          const bills = await getBillsByRoomId(room.room_id);
          console.log(
            "Room:",
            room.room_id,
            "Bills:",
            bills,
            "todayStr:",
            todayStr
          );
          // 1. Ưu tiên overdue: bill có ngày < hôm nay và chưa thanh toán
          const hasOverdue = bills.some(
            (b) => b.thang_hoa_don < todayStr && b.payment_status === "pending"
          );
          if (hasOverdue) {
            statusMap[room.room_id] = "overdue";
            console.log("Room", room.room_id, "set to overdue");
            return;
          }
          // 2. Nếu bill đúng ngày hôm nay
          const billToday = bills.find((b) => b.thang_hoa_don === todayStr);
          if (billToday) {
            if (billToday.payment_status === "pending") {
              statusMap[room.room_id] = "pending";
              return;
            }
            if (billToday.payment_status === "paid") {
              statusMap[room.room_id] = "paid";
              return;
            }
          }
          // 3. Nếu tất cả bill trước hôm nay đều paid (và không có bill hôm nay)
          const allBeforeTodayPaid =
            bills.filter((b) => b.thang_hoa_don < todayStr).length > 0 &&
            bills
              .filter((b) => b.thang_hoa_don < todayStr)
              .every((b) => b.payment_status === "paid");
          if (allBeforeTodayPaid) {
            statusMap[room.room_id] = "paid";
            console.log(
              "Room",
              room.room_id,
              "set to paid (all before today paid)"
            );
            return;
          }
          // 4. Không có bill hoặc trạng thái khác
          statusMap[room.room_id] = undefined;
          console.log("Room", room.room_id, "set to undefined");
        })
      );
      setRoomBillStatus(statusMap);
    };
    fetchBills();
  }, [rooms, currentMonth]);

  // Tenants are now loaded in loadInitialData for better performance

  // Hàm lọc và sắp xếp tenants ưu tiên, lấy room_number và trạng thái bill
  const getTop3Tenants = () => {
    const today = new Date();
    // Lọc tenant chính và map room_number
    const mainTenants = tenants
      .filter((t) => t.is_main_tenant)
      .map((t) => {
        const room = rooms.find((r) => r.room_id === t.room_id);
        const room_number = room ? room.room_number : "";
        // Lấy trạng thái bill của phòng
        let status = "";
        // Ưu tiên bill overdue, sau đó pending, còn lại là paid
        if (room && roomBillStatus[room.room_id] === "overdue")
          status = "overdue";
        else if (room && roomBillStatus[room.room_id] === "pending")
          status = "expiring";
        else status = "paid";
        return {
          ...t,
          room_number,
          status,
        };
      });
    // Ưu tiên: overdue > expiring > paid
    mainTenants.sort((a, b) => {
      const order = { overdue: 0, expiring: 1, paid: 2 };
      return (
        order[a.status as keyof typeof order] -
        order[b.status as keyof typeof order]
      );
    });
    return mainTenants.slice(0, 3);
  };

  const pieData = [
    {
      name: t("report.rented"),
      population: roomStats.occupied,
      color: "#70C4D7",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.empty"),
      population: roomStats.available,
      color: "#FFC107",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.underRepair"),
      population: roomStats.maintenance,
      color: "#4CAF50",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
  ];

  // Room stats are now loaded in loadInitialData for better performance

  // Hàm lấy doanh thu từng tháng cho biểu đồ (có debug log)
  const fetchMonthlyRevenue = async (months: number) => {
    if (!motelId) return;
    const now = new Date();
    const result: { label: string; value: number }[] = [];
    for (let i = months - 1; i >= 0; i--) {
      const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const month = d.getMonth() + 1;
      const year = d.getFullYear();
      const label = `${month.toString().padStart(2, "0")}/${year}`;
      console.log(`[DEBUG] Tính doanh thu cho tháng:`, {
        motelId,
        month,
        year,
        label,
      });
      const value = await getMonthlyRevenue(motelId, month, year);
      console.log(`[DEBUG] Doanh thu tháng ${label}:`, value);
      result.push({ label, value });
    }
    console.log("[DEBUG] Kết quả doanh thu các tháng:", result);
    setMonthlyRevenue(result);
  };

  useEffect(() => {
    fetchMonthlyRevenue(revenueMonths);
  }, [motelId, revenueMonths]);

  // Fetch monthly statistics when selectedMonth or motelId changes
  useEffect(() => {
    if (!motelId || !selectedMonth) return;
    getAverageRevenueByMonth(
      motelId,
      selectedMonth.month,
      selectedMonth.year
    ).then(setAverageRevenue);
    getTotalUnpaidByMonth(
      motelId,
      selectedMonth.month,
      selectedMonth.year
    ).then(setMonthlyUnpaid);
    getOnTimePaymentRateByMonth(
      motelId,
      selectedMonth.month,
      selectedMonth.year
    ).then(setOnTimeRate);
    getRentalRateByMonth(motelId, selectedMonth.month, selectedMonth.year).then(
      setRentalRate
    );
    getRevenueGrowthRate(motelId, selectedMonth.month, selectedMonth.year).then(
      setGrowthRate
    );
    getMonthlyRevenue(motelId, selectedMonth.month, selectedMonth.year).then(
      setRevenueAmount
    );
    getTotalUnpaid(motelId).then(setUnpaidAmount); // Optionally update unpaid for all months
  }, [motelId, selectedMonth]);

  // Fetch previous month statistics
  useEffect(() => {
    if (!motelId || !selectedMonth) return;
    let prevMonth = selectedMonth.month - 1;
    let prevYear = selectedMonth.year;
    if (prevMonth === 0) {
      prevMonth = 12;
      prevYear--;
    }
    getAverageRevenueByMonth(motelId, prevMonth, prevYear).then(
      setPrevAverageRevenue
    );
    getTotalUnpaidByMonth(motelId, prevMonth, prevYear).then(
      setPrevMonthlyUnpaid
    );
    getOnTimePaymentRateByMonth(motelId, prevMonth, prevYear).then(
      setPrevOnTimeRate
    );
    getRentalRateByMonth(motelId, prevMonth, prevYear).then(setPrevRentalRate);
  }, [motelId, selectedMonth]);

  // Optimized focus effect - only refresh data that might have changed
  useFocusEffect(
    React.useCallback(() => {
      if (motelId) {
        // Only refresh data that might have changed while away
        loadInitialData();
        // Đăng ký lắng nghe rooms, trả về hàm unsubscribe khi rời khỏi màn hình
        const unsubscribe = subscribeToRooms(setRooms, motelId);
        return () => {
          if (unsubscribe) unsubscribe();
        };
      }
    }, [motelId, loadInitialData])
  );

  if (isLoading) {
    return (
      <View
        style={[
          styles.container,
          styles.loadingContainer,
          { backgroundColor: colors.BACKGROUND },
        ]}
      >
        <ActivityIndicator size="large" color="#70C4D7" />
        <Text style={[styles.loadingText, { color: colors.TEXT_PRIMARY }]}>
          {t("common.loading")}...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND }]}>
      <StatusBar
        backgroundColor="#003366"
        barStyle={isDarkMode ? "light-content" : "dark-content"}
      />
      {renderHeader()}
      {activeTab === "operations" ? renderOperationsTab() : renderFinanceTab()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: "#003366",
    paddingTop: 40,
    paddingBottom: 0,
    paddingHorizontal: 16,
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 16,
    zIndex: 10,
  },
  headerTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 0,
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomColor: "#FFFFFF",
  },
  tabText: {
    color: "#FFFFFF",
    opacity: 0.7,
    fontSize: 16,
  },
  activeTabText: {
    color: "#FFFFFF",
    opacity: 1,
    fontWeight: "bold",
  },
  tabContent: {
    flex: 1,
  },
  subTabContainer: {
    flexDirection: "row",
    backgroundColor: "#003366",
    paddingHorizontal: 16,
  },
  subTabButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    position: "relative",
  },
  activeSubTabButton: {},
  subTabText: {
    color: "#FFFFFF",
    opacity: 0.7,
    fontSize: 16,
  },
  activeSubTabText: {
    color: "#FFFFFF",
    opacity: 1,
    fontWeight: "bold",
  },
  subTabIndicator: {
    position: "absolute",
    bottom: 0,
    left: 24,
    right: 24,
    height: 3,
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  dateRangeCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  datePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#F5F5F5",
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
    width: 100,
  },
  sectionCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  statusFiltersContainer: {
    marginBottom: 16,
  },
  statusFilters: {
    flexDirection: "row",
    paddingVertical: 8,
  },
  statusFilter: {
    marginRight: 8,
  },
  roomCountContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  roomCount: {
    fontSize: 24,
    fontWeight: "bold",
    marginLeft: 8,
  },
  roomCountLabel: {
    marginLeft: 4,
  },
  truyVanButton: {
    marginLeft: "auto",
    backgroundColor: "#003366",
  },
  truyVanButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
  },
  floorContainer: {
    marginBottom: 16,
  },
  floorTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  roomGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  roomItem: {
    width: 70,
    height: 70,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  roomNumber: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  tenantListLabel: {
    fontSize: 12,
    marginBottom: 16,
  },
  tenantList: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 8,
  },
  tenantItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  tenantRoom: {
    fontWeight: "500",
  },
  tenantStatus: {
    fontWeight: "bold",
  },
  divider: {
    height: 1,
    backgroundColor: "#E0E0E0",
  },
  overviewCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  overviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
  },
  statLabel: {
    fontSize: 12,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  chartFilterContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  legendContainer: {
    marginTop: 16,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  maintenanceCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  maintenanceList: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 8,
  },
  maintenanceItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  maintenanceRoom: {
    fontWeight: "500",
  },
  maintenanceStatus: {
    fontWeight: "bold",
  },
  revenueCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  revenueHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  revenueHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  revenueTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  revenueAmount: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  revenuePeriod: {
    fontSize: 12,
  },
  percentageContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 8,
  },
  percentageBadge: {
    flexDirection: "row",
    alignItems: "center",
  },
  revenueGridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    margin: 16,
    marginTop: 0,
    gap: 8,
  },
  revenueGridItem: {
    width: "48%",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  revenueGridTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    marginBottom: 8,
  },
  revenueGridValue: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  chartCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  expenseCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  expenseHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  expenseHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  expenseTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  expenseAmount: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  expensePeriod: {
    fontSize: 12,
  },
  chartHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  pieChartContainer: {
    position: "relative",
    alignItems: "center",
    marginVertical: 20,
  },
  pieChartCenterText: {
    position: "absolute",
    top: "50%",
    left: "50%",
    width: 100,
    height: 100,
    marginLeft: -50,
    marginTop: -50,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  pieChartCenterLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  pieChartCenterValue: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
  },
  expenseTableContainer: {
    marginTop: 20,
  },
  expenseTableHeader: {
    flexDirection: "row",
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
    marginBottom: 12,
  },
  expenseTableHeaderText: {
    fontSize: 14,
  },
  expenseTableRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  expenseCategoryContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 2,
  },
  expenseCategoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  expenseCategoryText: {
    fontSize: 14,
  },
  expenseValueText: {
    fontSize: 14,
  },
  expensePercentText: {
    fontSize: 14,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default ReportScreen;
