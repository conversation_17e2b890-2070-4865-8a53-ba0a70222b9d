import React, { createContext, useState, useEffect, useContext } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import i18n from "../i18n/i18n";

interface LanguageContextType {
  currentLanguage: string;
  changeLanguage: (language: string) => void;
  t: (scope: string, options?: object) => string;
}

export const LanguageContext = createContext<LanguageContextType>({
  currentLanguage: "vi",
  changeLanguage: () => {},
  t: () => "",
});

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [currentLanguage, setCurrentLanguage] = useState("vi");

  // Load saved language preference
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem("language");
        if (savedLanguage) {
          setCurrentLanguage(savedLanguage);
          i18n.locale = savedLanguage;
        }
      } catch (error) {
        console.error("Failed to load language preference:", error);
      }
    };

    loadLanguage();
  }, []);

  // Change language function
  const changeLanguage = async (language: string) => {
    try {
      await AsyncStorage.setItem("language", language);
      setCurrentLanguage(language);
      i18n.locale = language;
    } catch (error) {
      console.error("Failed to save language preference:", error);
    }
  };

  // Translation function with safety check
  const t = (scope: string, options: object = {}) => {
    try {
      const translation = i18n.t(scope, options);
      // Ensure we always return a string, never undefined or null
      return translation || scope || "";
    } catch (error) {
      console.warn(`Translation error for key: ${scope}`, error);
      return scope || "";
    }
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook for using language context
export const useLanguage = () => useContext(LanguageContext);
