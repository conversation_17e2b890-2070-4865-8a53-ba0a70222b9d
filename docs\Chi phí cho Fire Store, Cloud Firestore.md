# Để lưu ảnh cần có **Firebase Storage** mà nó chỉ cho phép dùng khi bật gói **Blaze Plan**

### Gói Free (Spark) của Firebase Storage
- Dung lượng lưu trữ tối đa: **1 GB**
- Băng thông tải xuống miễn phí: **5 GB/tháng**
- Số lượng file: Không giới hạn, miễn tổng dung lượng không vượt quá 1 GB
- Kích thước file tối đa: 10 MB/file (nên nén ảnh avatar còn ~100-300 KB)

### Ước tính số lượng user/phòng trọ tối đa với gói Free
- Nếu mỗi user chỉ có 1 ảnh đại diện (~200 KB):
  - 1 GB = 1.000.000 KB / 200 KB ≈ **5.000 user**
- Nếu mỗi user quản lý 10 phòng, mỗi phòng 1 ảnh (~200 KB/ảnh):
  - 1 user + 10 phòng = 11 ảnh x 200 KB = 2.200 KB/user
  - 1.000.000 KB / 2.200 KB ≈ **450 user**
- Nếu ảnh nén tốt (~100 KB/ảnh):
  - 1.000.000 KB / 100 KB = **10.000 ảnh** (user + phòng cộng lại)

=> Có thể lưu 5000 ảnh (Với dung lượng trung bình 200KB)
Một link ảnh được cắt ~100kb (ảnh avatar)
![alt text](https://firebasestorage.googleapis.com/v0/b/quanlynhatro-8afcf.firebasestorage.app/o/avatars%2FXwGRG1752NcYeDXNPWz8IO40jLF2%2Favatar_1750935612427.jpg?alt=media&token=e6f1d5cb-4050-47c2-9bd5-f4072a141ad9)

=> Nếu dùng ảnh luôn cho cả dãy trọ thì sẽ không handle được >2000 User
---

## Cloud Firestore (Dữ liệu user, phòng, hóa đơn...)
- Dung lượng lưu trữ miễn phí: **1 GB**
- Số lần ghi (write) miễn phí: **20.000 lần/ngày**
- Số lần đọc (read) miễn phí: **50.000 lần/ngày**
- Số lần xóa (delete) miễn phí: **20.000 lần/ngày**

### Ước tính số lượng user và phòng tối đa với gói Free
- Mỗi user, phòng, hóa đơn, log... là 1 document, thường chỉ vài KB/document.
- 1 GB = 1.000.000 KB
- Nếu mỗi user, phòng, hóa đơn, log ~2 KB:
  - 1.000.000 KB / 2 KB = **500.000 document** (user + phòng + hóa đơn + log cộng lại)
- Nếu hệ thống có 5.000 user, mỗi user quản lý 10 phòng:
  - **5.000 user** + 50.000 phòng = 55.000 document (chưa tính hóa đơn, log)
- Nếu mỗi tháng mỗi phòng có 1 hóa đơn, 1 năm = 12 hóa đơn/phòng:
  - 50.000 phòng x 12 = 600.000 hóa đơn/năm (vượt mức 1 GB nếu lưu lâu dài)

=> Lâu dài sẽ cần phải lên gói Blaze (Nếu user >2000)

### Kết luận Firestore:
- Gói Free đủ cho MVP, app nhỏ, hoặc vài ngàn user, vài chục ngàn phòng, vài trăm ngàn hóa đơn nếu dữ liệu nhỏ/gọn.
- Nếu lưu lâu dài, cần xóa dữ liệu cũ hoặc nâng lên Blaze.
- Số lần đọc/ghi/ngày rất lớn, khó vượt nếu chỉ dùng cho app quản lý nhà trọ thông thường.

# Tóm lại
**User: ~2000**
**Document: ~55.000 Document**
**Image: ~5000**

App chắc không tới 1000 người dùng đâu chắc khỏi cần lo. Đã áp rules chống mấy thằng phá nên cũng chẳng cần lo lắm