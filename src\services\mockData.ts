import {
  User,
  Mo<PERSON>,
  Room,
  Resident,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "../types";

// Mock Users
export const mockUsers: User[] = [
  {
    user_id: "user1",
    email: "<EMAIL>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    phone: "0912345678",
    managed_motels: ["motel1"],
    created_at: new Date("2023-01-01"),
    role: "owner",
  },
  {
    user_id: "user2",
    email: "<EMAIL>",
    name: "<PERSON><PERSON><PERSON><PERSON> Lý",
    phone: "0987654321",
    managed_motels: ["motel1"],
    created_at: new Date("2023-02-15"),
    role: "manager",
  },
];

// Mock Motels
export const mockMotels: Motel[] = [
  {
    motel_id: "motel1",
    owner_id: "user1",
    name: "<PERSON><PERSON><PERSON> tr<PERSON>IT",
    address: "<PERSON><PERSON><PERSON>, TP.HCM",
    managerUids: ["user2"],
    managerEmails: ["<EMAIL>"],
    created_at: new Date("2023-01-15"),
  },
];

// Mock Rooms
export const mockRooms: Room[] = [
  {
    room_id: "room1",
    motel_id: "motel1",
    room_number: "A101",
    gia: 2500000,
    tienCoc: 2500000,
    tenant_id: "resident1",
    created_at: new Date("2023-02-01"),
    start_date: new Date("2023-03-01"),
    end_date: null,
    ghiChu: "Phòng đẹp, view đường",
    dienTich: 25,
    loaiPhong: ["Phòng đôi", "Có gác"],
    tienNghi: ["Máy lạnh", "Tủ lạnh", "Wifi"],
    status: "occupied",
  },
  {
    room_id: "room2",
    motel_id: "motel1",
    room_number: "A102",
    gia: 2000000,
    tienCoc: 2000000,
    tenant_id: null,
    created_at: new Date("2023-02-01"),
    start_date: null,
    end_date: null,
    ghiChu: "Phòng mới sửa",
    dienTich: 20,
    loaiPhong: ["Phòng đơn"],
    tienNghi: ["Máy lạnh", "Wifi"],
    status: "available",
  },
  {
    room_id: "room3",
    motel_id: "motel1",
    room_number: "A103",
    gia: 3000000,
    tienCoc: 3000000,
    tenant_id: "resident3",
    created_at: new Date("2023-02-01"),
    start_date: new Date("2023-04-01"),
    end_date: null,
    ghiChu: "Phòng rộng, có ban công",
    dienTich: 30,
    loaiPhong: ["Phòng đôi", "Có gác", "Studio"],
    tienNghi: ["Máy lạnh", "Tủ lạnh", "Wifi", "Máy giặt", "TV"],
    status: "occupied",
  },
  {
    room_id: "room4",
    motel_id: "motel1",
    room_number: "A104",
    gia: 2200000,
    tienCoc: 2200000,
    tenant_id: null,
    created_at: new Date("2023-02-01"),
    start_date: null,
    end_date: null,
    ghiChu: "Đang sửa chữa",
    dienTich: 22,
    loaiPhong: ["Phòng đơn", "Có gác"],
    tienNghi: ["Máy lạnh", "Wifi"],
    status: "maintenance",
  },
  {
    room_id: "room5",
    motel_id: "motel1",
    room_number: "A105",
    gia: 2800000,
    tienCoc: 2800000,
    tenant_id: "resident5",
    created_at: new Date("2023-02-01"),
    start_date: new Date("2023-05-01"),
    end_date: null,
    ghiChu: "Phòng đẹp, view hồ bơi",
    dienTich: 28,
    loaiPhong: ["Phòng đôi", "Studio"],
    tienNghi: ["Máy lạnh", "Tủ lạnh", "Wifi", "TV"],
    status: "occupied",
  },
  {
    room_id: "room6",
    motel_id: "motel1",
    room_number: "A106",
    gia: 1800000,
    tienCoc: 1800000,
    tenant_id: null,
    created_at: new Date("2023-02-01"),
    start_date: null,
    end_date: null,
    ghiChu: "Phòng nhỏ, giá rẻ",
    dienTich: 18,
    loaiPhong: ["Phòng đơn"],
    tienNghi: ["Quạt", "Wifi"],
    status: "available",
  },
];

// Mock Residents
export const mockResidents: Resident[] = [
  {
    resident_id: "resident1",
    room_id: "room1",
    name: "An",
    phone: "0912345678",
    identity_number: "123456789012",
    is_main_tenant: true,
    created_at: new Date("2023-03-01"),
  },
  {
    resident_id: "resident2",
    room_id: "room1",
    name: "Trần Thị B",
    phone: "0987654321",
    identity_number: "123456789013",
    is_main_tenant: false,
    created_at: new Date("2023-03-01"),
  },
  {
    resident_id: "resident3",
    room_id: "room3",
    name: "Lê Văn C",
    phone: "0912345679",
    identity_number: "123456789014",
    is_main_tenant: true,
    created_at: new Date("2023-04-01"),
  },
  {
    resident_id: "resident4",
    room_id: "room3",
    name: "Phạm Thị D",
    phone: "0987654322",
    identity_number: "123456789015",
    is_main_tenant: false,
    created_at: new Date("2023-04-01"),
  },
  {
    resident_id: "resident5",
    room_id: "room5",
    name: "Hoàng Văn E",
    phone: "0912345680",
    identity_number: "123456789016",
    is_main_tenant: true,
    created_at: new Date("2023-05-01"),
  },
];

// Mock GiaDienNuoc
export const mockGiaDienNuoc: GiaDienNuoc[] = [
  {
    motel_id: "motel1",
    electricity_price: 3500,
    water_price: 15000,
    updated_at: new Date("2023-01-15"),
  },
];

// Mock ChiSoDienNuoc
export const mockChiSoDienNuoc: ChiSoDienNuoc[] = [
  {
    record_id: "record1",
    phong_id: "room1",
    motel_id: "motel1",
    thang_nam: "05/2023",
    chi_so_dien_cu: 100,
    chi_so_dien_moi: 200,
    chi_so_nuoc_cu: 10,
    chi_so_nuoc_moi: 20,
    created_at: new Date("2023-05-31"),
  },
  {
    record_id: "record2",
    phong_id: "room1",
    motel_id: "motel1",
    thang_nam: "06/2023",
    chi_so_dien_cu: 200,
    chi_so_dien_moi: 300,
    chi_so_nuoc_cu: 20,
    chi_so_nuoc_moi: 30,
    created_at: new Date("2023-06-30"),
  },
  {
    record_id: "record3",
    phong_id: "room3",
    motel_id: "motel1",
    thang_nam: "05/2023",
    chi_so_dien_cu: 0,
    chi_so_dien_moi: 150,
    chi_so_nuoc_cu: 0,
    chi_so_nuoc_moi: 15,
    created_at: new Date("2023-05-31"),
  },
  {
    record_id: "record4",
    phong_id: "room3",
    motel_id: "motel1",
    thang_nam: "06/2023",
    chi_so_dien_cu: 150,
    chi_so_dien_moi: 280,
    chi_so_nuoc_cu: 15,
    chi_so_nuoc_moi: 28,
    created_at: new Date("2023-06-30"),
  },
  {
    record_id: "record5",
    phong_id: "room5",
    motel_id: "motel1",
    thang_nam: "05/2023",
    chi_so_dien_cu: 0,
    chi_so_dien_moi: 120,
    chi_so_nuoc_cu: 0,
    chi_so_nuoc_moi: 12,
    created_at: new Date("2023-05-31"),
  },
  {
    record_id: "record6",
    phong_id: "room5",
    motel_id: "motel1",
    thang_nam: "06/2023",
    chi_so_dien_cu: 120,
    chi_so_dien_moi: 250,
    chi_so_nuoc_cu: 12,
    chi_so_nuoc_moi: 25,
    created_at: new Date("2023-06-30"),
  },
];

// Mock HoaDon
export const mockHoaDon: HoaDon[] = [
  {
    hoadon_id: "hoadon1",
    phong_id: "room1",
    motel_id: "motel1",
    tenant_id: "resident1",
    thang_hoa_don: "05/2023",
    tienphong: 2500000,
    tiendien: 350000, // (200-100) * 3500
    tiennuoc: 150000, // (20-10) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3150000,
    tiennuoc_moi: 150000,
    auto_generated: true,
    created_at: new Date("2023-05-31"),
    due_date: new Date("2023-06-10"),
    overdue_days: 0,
    payment_status: "paid",
  },
  {
    hoadon_id: "hoadon2",
    phong_id: "room1",
    motel_id: "motel1",
    tenant_id: "resident1",
    thang_hoa_don: "06/2023",
    tienphong: 2500000,
    tiendien: 350000, // (300-200) * 3500
    tiennuoc: 150000, // (30-20) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3150000,
    tiennuoc_moi: 150000,
    auto_generated: true,
    created_at: new Date("2023-06-30"),
    due_date: new Date("2023-07-10"),
    overdue_days: 0,
    payment_status: "pending",
  },
  {
    hoadon_id: "hoadon3",
    phong_id: "room3",
    motel_id: "motel1",
    tenant_id: "resident3",
    thang_hoa_don: "05/2023",
    tienphong: 3000000,
    tiendien: 525000, // (150-0) * 3500
    tiennuoc: 225000, // (15-0) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3900000,
    tiennuoc_moi: 225000,
    auto_generated: true,
    created_at: new Date("2023-05-31"),
    due_date: new Date("2023-06-10"),
    overdue_days: 0,
    payment_status: "paid",
  },
  {
    hoadon_id: "hoadon4",
    phong_id: "room3",
    motel_id: "motel1",
    tenant_id: "resident3",
    thang_hoa_don: "06/2023",
    tienphong: 3000000,
    tiendien: 455000, // (280-150) * 3500
    tiennuoc: 195000, // (28-15) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3800000,
    tiennuoc_moi: 195000,
    auto_generated: true,
    created_at: new Date("2023-06-30"),
    due_date: new Date("2023-07-10"),
    overdue_days: 5,
    payment_status: "overdue",
  },
  {
    hoadon_id: "hoadon5",
    phong_id: "room5",
    motel_id: "motel1",
    tenant_id: "resident5",
    thang_hoa_don: "05/2023",
    tienphong: 2800000,
    tiendien: 420000, // (120-0) * 3500
    tiennuoc: 180000, // (12-0) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3550000,
    tiennuoc_moi: 180000,
    auto_generated: true,
    created_at: new Date("2023-05-31"),
    due_date: new Date("2023-06-10"),
    overdue_days: 0,
    payment_status: "paid",
  },
  {
    hoadon_id: "hoadon6",
    phong_id: "room5",
    motel_id: "motel1",
    tenant_id: "resident5",
    thang_hoa_don: "06/2023",
    tienphong: 2800000,
    tiendien: 455000, // (250-120) * 3500
    tiennuoc: 195000, // (25-12) * 15000
    giadien: 3500,
    gianuoc: 15000,
    additional_fees: [
      { name: "Internet", amount: 100000 },
      { name: "Rác", amount: 50000 },
    ],
    tong_tien: 3600000,
    tiennuoc_moi: 195000,
    auto_generated: true,
    created_at: new Date("2023-06-30"),
    due_date: new Date("2023-07-10"),
    overdue_days: 0,
    payment_status: "pending",
  },
];

// Helper functions to get data
export const getMockRoomById = (roomId: string): Room | undefined => {
  return mockRooms.find((room) => room.room_id === roomId);
};

export const getResidentsByRoomId = (roomId: string): Resident[] => {
  return mockResidents.filter((resident) => resident.room_id === roomId);
};

export const getHoaDonByRoomId = (roomId: string): HoaDon[] => {
  return mockHoaDon.filter((hoadon) => hoadon.phong_id === roomId);
};

export const getChiSoDienNuocByRoomId = (roomId: string): ChiSoDienNuoc[] => {
  return mockChiSoDienNuoc.filter((record) => record.phong_id === roomId);
};

export const getGiaDienNuocByMotelId = (
  motelId: string
): GiaDienNuoc | undefined => {
  return mockGiaDienNuoc.find((record) => record.motel_id === motelId);
};

export const getMotelById = (motelId: string): Motel | undefined => {
  return mockMotels.find((motel) => motel.motel_id === motelId);
};

export const getUserById = (userId: string): User | undefined => {
  return mockUsers.find((user) => user.user_id === userId);
};
