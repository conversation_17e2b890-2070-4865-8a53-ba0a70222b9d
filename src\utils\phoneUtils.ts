import { Alert, Linking } from "react-native";

/**
 * Utility functions for phone-related operations
 */

/**
 * Clean phone number by removing all non-digit characters except +
 * @param phoneNumber - The phone number to clean
 * @returns Cleaned phone number
 */
export const cleanPhoneNumber = (phoneNumber: string): string => {
  return phoneNumber.replace(/[^\d+]/g, "");
};

/**
 * Validate if a phone number is valid
 * @param phoneNumber - The phone number to validate
 * @returns True if valid, false otherwise
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const cleaned = cleanPhoneNumber(phoneNumber);
  // Check if it has at least 10 digits (minimum for most phone numbers)
  return cleaned.length >= 10;
};

/**
 * Format phone number for display
 * @param phoneNumber - The phone number to format
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  const cleaned = cleanPhoneNumber(phoneNumber);

  // Vietnamese phone number format
  if (cleaned.startsWith("84")) {
    // International format: +84 xxx xxx xxx
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(
      5,
      8
    )} ${cleaned.slice(8)}`;
  } else if (cleaned.startsWith("0") && cleaned.length === 10) {
    // Local format: 0xxx xxx xxx
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
  }

  // Return as is if format is not recognized
  return phoneNumber;
};

/**
 * Make a phone call using the device's default phone app
 * @param phoneNumber - The phone number to call
 * @param contactName - The name of the contact (for confirmation dialog)
 * @param t - Translation function
 * @returns Promise<boolean> - True if call was initiated successfully
 */
export const makePhoneCall = async (
  phoneNumber: string,
  contactName: string,
  t: (key: string, options?: any) => string
): Promise<boolean> => {
  try {
    // Clean the phone number
    const cleanedNumber = cleanPhoneNumber(phoneNumber);

    // Validate phone number
    if (!isValidPhoneNumber(cleanedNumber)) {
      Alert.alert(t("common.error"), t("room.invalidPhoneNumber"));
      return false;
    }

    // Create the phone URL
    const phoneUrl = `tel:${cleanedNumber}`;

    // Check if the device can handle phone calls
    const canOpen = await Linking.canOpenURL(phoneUrl);

    if (!canOpen) {
      Alert.alert(t("common.error"), t("room.phoneNotSupported"));
      return false;
    }

    // Show confirmation dialog and make the call
    return new Promise((resolve) => {
      Alert.alert(
        t("room.confirmCall"),
        t("room.confirmCallMessage", {
          name: contactName,
          phone: formatPhoneNumber(phoneNumber),
        }),
        [
          {
            text: t("common.cancel"),
            style: "cancel",
            onPress: () => resolve(false),
          },
          {
            text: t("room.call"),
            onPress: async () => {
              try {
                await Linking.openURL(phoneUrl);
                resolve(true);
              } catch (error) {
                console.error("Error making phone call:", error);
                Alert.alert(t("common.error"), t("room.callError"));
                resolve(false);
              }
            },
          },
        ]
      );
    });
  } catch (error) {
    console.error("Error in makePhoneCall:", error);
    Alert.alert(t("common.error"), t("room.callError"));
    return false;
  }
};

/**
 * Open SMS app with pre-filled phone number
 * @param phoneNumber - The phone number to send SMS to
 * @param message - Optional pre-filled message
 * @param t - Translation function
 * @returns Promise<boolean> - True if SMS app was opened successfully
 */
export const sendSMS = async (
  phoneNumber: string,
  message: string = "",
  t: (key: string, options?: any) => string
): Promise<boolean> => {
  try {
    // Clean the phone number
    const cleanedNumber = cleanPhoneNumber(phoneNumber);

    // Validate phone number
    if (!isValidPhoneNumber(cleanedNumber)) {
      Alert.alert(t("common.error"), t("room.invalidPhoneNumber"));
      return false;
    }

    // Create the SMS URL
    const smsUrl = `sms:${cleanedNumber}${
      message ? `?body=${encodeURIComponent(message)}` : ""
    }`;

    // Check if the device can handle SMS
    const canOpen = await Linking.canOpenURL(smsUrl);

    if (!canOpen) {
      Alert.alert(t("common.error"), t("room.smsNotSupported"));
      return false;
    }

    await Linking.openURL(smsUrl);
    return true;
  } catch (error) {
    console.error("Error sending SMS:", error);
    Alert.alert(t("common.error"), t("room.smsError"));
    return false;
  }
};
