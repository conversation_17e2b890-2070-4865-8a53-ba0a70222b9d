import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Surface,
  List,
  Divider,
  Button,
  Portal,
  Dialog,
  Switch,
  RadioButton,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography, Spacing } from "../../theme";
import { auth } from "../../../config/firebase";
import { signOut } from "firebase/auth";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { OptimizedTextInput } from "../../components/OptimizedTextInput";
import { getUserById } from "../../services/UserServices";
import { useFocusEffect } from "@react-navigation/native";
import { changeUserPassword } from "../../services/changePassword";
import { useDefaultPrices } from "../../context/DefaultPricesContext";

const ProfileScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { currentLanguage, changeLanguage, t } = useLanguage();
  const { isDarkMode, toggleTheme } = useTheme();
  const {
    defaultElectricityPrice,
    defaultWaterPrice,
    defaultServices,
    setDefaultElectricityPrice,
    setDefaultWaterPrice,
    addDefaultService,
    updateDefaultService,
    removeDefaultService,
  } = useDefaultPrices();

  // States for dialogs
  const [passwordDialogVisible, setPasswordDialogVisible] = useState(false);
  const [languageDialogVisible, setLanguageDialogVisible] = useState(false);
  const [defaultPricesDialogVisible, setDefaultPricesDialogVisible] =
    useState(false);

  // States for service management
  const [editingServiceId, setEditingServiceId] = useState<string | null>(null);
  const [newServiceName, setNewServiceName] = useState("");
  const [newServicePrice, setNewServicePrice] = useState("");

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [selectedLanguage, setSelectedLanguage] = useState(currentLanguage);

  const [user, setUser] = useState<any>(null);

  // Update selectedLanguage when currentLanguage changes
  useEffect(() => {
    setSelectedLanguage(currentLanguage);
  }, [currentLanguage]);

  // Optimized user data fetching
  const fetchUser = useCallback(async () => {
    if (auth.currentUser?.uid) {
      try {
        const userData = await getUserById(auth.currentUser.uid);
        if (userData) {
          setUser({
            name: userData.displayName || "",
            email: userData.email || "",
            role: t("profile.role"),
            avatar:
              userData.avatar ||
              "https://randomuser.me/api/portraits/men/32.jpg",
          });
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    }
  }, [auth.currentUser?.uid, t]);

  useFocusEffect(
    React.useCallback(() => {
      fetchUser();
    }, [fetchUser])
  );

  const handleEditHistoryPress = useCallback(() => {
    navigation.navigate("EditHistoryScreen");
  }, [navigation]);

  // Memoized menu items for better performance
  const squareMenuItems = useMemo(
    () => [
      {
        title: t("profile.profile"),
        icon: "account-outline",
        onPress: () => navigation.navigate("UpdatePersonalInfo"),
      },
      {
        title: t("profile.building"),
        icon: "office-building-outline",
        onPress: () => navigation.navigate("MotelsManagement"),
      },
      {
        title: t("profile.editHistory"),
        icon: "history",
        onPress: handleEditHistoryPress,
      },
      {
        title: t("profile.privacyPolicy"),
        icon: "shield-account-outline",
        onPress: () => console.log("Privacy policy pressed"),
      },
    ],
    [t, navigation, handleEditHistoryPress]
  );

  const listMenuItems = [
    {
      title: t("profile.changePassword"),
      icon: "lock-outline",
      onPress: () => setPasswordDialogVisible(true),
      type: "dialog",
    },
    {
      title: t("profile.language"),
      icon: "translate",
      onPress: () => setLanguageDialogVisible(true),
      type: "dialog",
    },
    {
      title: t("profile.defaultPrices"),
      icon: "currency-usd",
      onPress: () => setDefaultPricesDialogVisible(true),
      type: "dialog",
    },
    {
      title: t("profile.darkMode"),
      icon: "theme-light-dark",
      onPress: toggleTheme,
      type: "toggle",
      value: isDarkMode,
    },
    {
      title: t("profile.termsOfUse"),
      icon: "file-document-outline",
      onPress: () => console.log("Terms of use pressed"),
      type: "navigate",
    },
  ];

  const handleLogout = async () => {
    try {
      await signOut(auth);
      // Firebase Auth's onAuthStateChanged listener in RootNavigator
      // will automatically redirect to Auth navigator
    } catch (error) {
      Alert.alert(t("common.error"), t("auth.logoutError"));
    }
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      Alert.alert("Lỗi", "Vui lòng nhập đầy đủ thông tin");
      return;
    }
    if (newPassword !== confirmPassword) {
      Alert.alert("Lỗi", "Mật khẩu mới và xác nhận không khớp");
      return;
    }
    const result = await changeUserPassword(currentPassword, newPassword);
    if (result.success) {
      Alert.alert("Thành công", "Đổi mật khẩu thành công!");
      setPasswordDialogVisible(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } else {
      Alert.alert("Lỗi", result.message);
    }
  };

  const handleChangeLanguage = () => {
    // Apply the language change
    changeLanguage(selectedLanguage);
    setLanguageDialogVisible(false);
  };

  const handleUpdateDefaultPrices = () => {
    setDefaultPricesDialogVisible(false);
  };

  const handleAddService = () => {
    if (
      newServiceName.trim() &&
      newServicePrice.trim() &&
      parseInt(newServicePrice) > 0
    ) {
      addDefaultService({
        name: newServiceName.trim(),
        price: newServicePrice.trim(),
      });
      setNewServiceName("");
      setNewServicePrice("");
    }
  };

  const handleUpdateService = (id: string, name: string, price: string) => {
    if (name.trim() && price.trim() && parseInt(price) > 0) {
      updateDefaultService(id, { name: name.trim(), price: price.trim() });
    }
  };

  const handleDeleteService = (id: string) => {
    removeDefaultService(id);
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      <StatusBar
        backgroundColor={Colors.PRIMARY}
        barStyle={isDarkMode ? "light-content" : "dark-content"}
      />

      <ScrollView style={styles.content}>
        <Surface
          style={[styles.userCard, isDarkMode && styles.darkSurface]}
          elevation={1}
        >
          <View style={styles.userInfo}>
            <Image source={{ uri: user?.avatar }} style={styles.avatar} />
            <View style={styles.userDetails}>
              <Text
                variant="titleLarge"
                style={[styles.userName, isDarkMode && styles.darkText]}
              >
                {user?.name}
              </Text>
              <Text
                variant="bodyMedium"
                style={[
                  styles.userEmail,
                  isDarkMode && styles.darkSecondaryText,
                ]}
              >
                {user?.email}
              </Text>
              <Text variant="bodySmall" style={styles.userRole}>
                {user?.role}
              </Text>
            </View>
          </View>
        </Surface>

        <View style={styles.squareMenuContainer}>
          {squareMenuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.squareMenuItem, isDarkMode && styles.darkSurface]}
              onPress={item.onPress}
            >
              <View
                style={[
                  styles.squareMenuIconContainer,
                  isDarkMode && styles.darkIconContainer,
                ]}
              >
                <MaterialCommunityIcons
                  name={item.icon as any}
                  size={28}
                  color={Colors.PRIMARY}
                />
              </View>
              <Text
                style={[styles.squareMenuText, isDarkMode && styles.darkText]}
              >
                {item.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Surface
          style={[styles.menuCard, isDarkMode && styles.darkSurface]}
          elevation={1}
        >
          {listMenuItems.map((item, index) => (
            <React.Fragment key={index}>
              <List.Item
                title={item.title}
                left={() => (
                  <MaterialCommunityIcons
                    name={item.icon as any}
                    size={24}
                    color={Colors.PRIMARY}
                    style={styles.menuIcon}
                  />
                )}
                right={() =>
                  item.type === "toggle" ? (
                    <Switch
                      value={item.value}
                      onValueChange={item.onPress}
                      color={Colors.PRIMARY}
                      style={{
                        height: 24,
                      }}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={24}
                      color={
                        isDarkMode ? Colors.DARK.GRAY_DARK : Colors.GRAY_DARK
                      }
                    />
                  )
                }
                onPress={item.onPress}
                style={styles.menuItem}
                titleStyle={[
                  styles.menuItemTitle,
                  isDarkMode && styles.darkText,
                ]}
              />
              {index < listMenuItems.length - 1 && (
                <Divider
                  style={[styles.divider, isDarkMode && styles.darkDivider]}
                />
              )}
            </React.Fragment>
          ))}
        </Surface>

        <Button
          mode="contained"
          icon="logout"
          onPress={handleLogout}
          style={styles.logoutButton}
          buttonColor={Colors.DANGER}
        >
          {t("auth.logout")}
        </Button>

        <View style={styles.versionContainer}>
          <Text
            variant="bodySmall"
            style={[styles.versionText, isDarkMode && styles.darkHintText]}
          >
            {t("profile.version")} 1.0.0
          </Text>
        </View>
      </ScrollView>

      <Portal>
        <Dialog
          visible={passwordDialogVisible}
          onDismiss={() => setPasswordDialogVisible(false)}
          style={[styles.dialog, isDarkMode && styles.darkDialog]}
        >
          <Dialog.Title>{t("profile.changePassword")}</Dialog.Title>
          <Dialog.Content>
            <OptimizedTextInput
              label={t("auth.currentPassword")}
              defaultValue={currentPassword}
              onChangeText={setCurrentPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
            <OptimizedTextInput
              label={t("auth.newPassword")}
              defaultValue={newPassword}
              onChangeText={setNewPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
            <OptimizedTextInput
              label={t("auth.confirmPassword")}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setPasswordDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleChangePassword}>
              {t("common.confirm")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={languageDialogVisible}
          onDismiss={() => setLanguageDialogVisible(false)}
          style={[styles.dialog, isDarkMode && styles.darkDialog]}
        >
          <Dialog.Title>{t("profile.language")}</Dialog.Title>
          <Dialog.Content>
            <RadioButton.Group
              onValueChange={setSelectedLanguage}
              value={selectedLanguage}
            >
              <View style={styles.radioItem}>
                <RadioButton value="vi" color={Colors.PRIMARY} />
                <Text style={isDarkMode ? styles.darkText : undefined}>
                  {t("auth.vietnamese")}
                </Text>
              </View>
              <View style={styles.radioItem}>
                <RadioButton value="en" color={Colors.PRIMARY} />
                <Text style={isDarkMode ? styles.darkText : undefined}>
                  {t("auth.english")}
                </Text>
              </View>
            </RadioButton.Group>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLanguageDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleChangeLanguage}>
              {t("common.confirm")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={defaultPricesDialogVisible}
          onDismiss={() => setDefaultPricesDialogVisible(false)}
          style={[styles.dialog, isDarkMode && styles.darkDialog]}
        >
          <Dialog.Title>{t("profile.defaultPrices")}</Dialog.Title>
          <ScrollView
            style={{
              paddingHorizontal: 20,
            }}
          >
            <OptimizedTextInput
              label={t("profile.defaultElectricityPrice")}
              value={defaultElectricityPrice}
              onChangeText={setDefaultElectricityPrice}
              keyboardType="numeric"
              style={styles.dialogInput}
              leftIcon="flash"
            />
            <OptimizedTextInput
              label={t("profile.defaultWaterPrice")}
              value={defaultWaterPrice}
              onChangeText={setDefaultWaterPrice}
              keyboardType="numeric"
              style={styles.dialogInput}
              leftIcon="water"
            />

            <Divider style={{ marginVertical: 20 }} />

            <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>
              {t("profile.defaultServices")}
            </Text>

            {/* Existing services list */}
            <View style={styles.servicesContainer}>
              {defaultServices.map((service) => (
                <View
                  key={service.id}
                  style={[
                    styles.serviceCard,
                    isDarkMode && styles.darkServiceCard,
                  ]}
                >
                  <View style={styles.serviceHeader}>
                    <View style={styles.serviceIcon}>
                      <Text style={styles.serviceIconText}>
                        {service.name.charAt(0).toUpperCase()}
                      </Text>
                    </View>
                    <View style={styles.serviceDetails}>
                      <Text
                        style={[
                          styles.serviceName,
                          isDarkMode && styles.darkText,
                        ]}
                      >
                        {service.name}
                      </Text>
                      <Text
                        style={[
                          styles.servicePrice,
                          isDarkMode && styles.darkSecondaryText,
                        ]}
                      >
                        {parseInt(service.price).toLocaleString()} VNĐ
                      </Text>
                    </View>
                    <View style={styles.serviceActions}>
                      <TouchableOpacity
                        onPress={() => {
                          setEditingServiceId(service.id);
                          setNewServiceName(service.name);
                          setNewServicePrice(service.price);
                        }}
                        style={[styles.actionButton, styles.editButton]}
                      >
                        <MaterialCommunityIcons
                          name="pencil"
                          size={16}
                          color="#70C4D7"
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          Alert.alert(
                            t("profile.deleteService"),
                            t("profile.confirmDeleteService"),
                            [
                              { text: t("common.cancel"), style: "cancel" },
                              {
                                text: t("common.delete"),
                                onPress: () => handleDeleteService(service.id),
                              },
                            ]
                          );
                        }}
                        style={[styles.actionButton, styles.deleteButton]}
                      >
                        <MaterialCommunityIcons
                          name="delete"
                          size={16}
                          color="#F44336"
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))}
            </View>

            {/* Add new service */}
            <View style={styles.addServiceSection}>
              <Text
                style={[styles.addServiceTitle, isDarkMode && styles.darkText]}
              >
                {t("profile.addServiceTitle")}
              </Text>
              <View style={styles.addServiceForm}>
                <OptimizedTextInput
                  label={t("profile.serviceName")}
                  value={newServiceName}
                  onChangeText={setNewServiceName}
                  style={styles.serviceInput}
                  mode="outlined"
                  leftIcon="cog"
                />
                <OptimizedTextInput
                  label={t("profile.servicePrice")}
                  value={newServicePrice}
                  onChangeText={setNewServicePrice}
                  keyboardType="numeric"
                  style={styles.serviceInput}
                  mode="outlined"
                  leftIcon="currency-usd"
                />
                <Button
                  mode="contained"
                  onPress={handleAddService}
                  style={styles.addButton}
                  disabled={!newServiceName.trim() || !newServicePrice.trim()}
                  buttonColor="#70C4D7"
                >
                  {t("common.add")}
                </Button>
              </View>
            </View>
          </ScrollView>
          <Dialog.Actions>
            <Button onPress={() => setDefaultPricesDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleUpdateDefaultPrices}>
              {t("common.save")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  darkContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 32,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  userCard: {
    borderRadius: Spacing.BORDER_RADIUS.xl,
    padding: Spacing.SPACING.xl,
    marginBottom: Spacing.SPACING.lg,
    backgroundColor: Colors.WHITE,
    ...Spacing.SHADOW.md,
  },
  darkSurface: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 85,
    height: 85,
    borderRadius: 42.5,
    marginRight: Spacing.SPACING.lg,
    borderWidth: 3,
    borderColor: Colors.PRIMARY,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.FONT_SIZE.xl + 2,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    marginBottom: Spacing.SPACING.xs,
    letterSpacing: 0.3,
  },
  darkText: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  userEmail: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    marginBottom: Spacing.SPACING.xs,
  },
  darkSecondaryText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  darkHintText: {
    color: Colors.DARK.TEXT_HINT,
  },
  userRole: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.PRIMARY,
    fontWeight: Typography.FONT_WEIGHT.semibold,
    backgroundColor: `${Colors.PRIMARY}15`,
    paddingHorizontal: Spacing.SPACING.sm,
    paddingVertical: Spacing.SPACING.xs,
    borderRadius: Spacing.BORDER_RADIUS.sm,
    alignSelf: "flex-start",
  },
  squareMenuContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: Spacing.SPACING.lg,
    paddingHorizontal: Spacing.SPACING.sm,
  },
  squareMenuItem: {
    width: "48%",
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    padding: Spacing.SPACING.lg,
    marginBottom: Spacing.SPACING.md,
    alignItems: "center",
    justifyContent: "center",
    ...Spacing.SHADOW.md,
  },
  squareMenuIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${Colors.PRIMARY}15`,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.SPACING.sm,
  },
  darkIconContainer: {
    backgroundColor: `${Colors.PRIMARY}25`,
  },
  squareMenuText: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
    textAlign: "center",
    marginTop: Spacing.SPACING.xs,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  menuCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    backgroundColor: Colors.WHITE,
  },
  menuItem: {
    paddingVertical: 12,
  },
  menuIcon: {
    marginLeft: 8,
  },
  menuItemTitle: {
    fontSize: Typography.FONT_SIZE.lg,
    color: Colors.TEXT_PRIMARY,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  darkDivider: {
    backgroundColor: Colors.DARK.DIVIDER,
  },
  dialog: {
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
    maxHeight: "90%",
    // scrollable
  },
  darkDialog: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  dialogInput: {
    marginBottom: 12,
  },
  dialogDescription: {
    marginBottom: 16,
    fontSize: 14,
    lineHeight: 20,
  },
  radioItem: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  logoutButton: {
    marginVertical: 16,
    borderRadius: 8,
    paddingVertical: 8,
  },
  versionContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  versionText: {
    color: Colors.TEXT_HINT,
  },
  // Service management styles
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: Colors.TEXT_PRIMARY,
  },
  servicesContainer: {
    marginBottom: 20,
    marginHorizontal: 4,
  },
  serviceCard: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  darkServiceCard: {
    backgroundColor: Colors.DARK.SURFACE,
  },
  serviceHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  serviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#70C4D7",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  serviceIconText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
  serviceDetails: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 4,
  },
  servicePrice: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY,
    fontWeight: "500",
  },
  serviceActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  editButton: {
    backgroundColor: "#E3F2FD",
  },
  deleteButton: {
    backgroundColor: "#FFEBEE",
  },
  addServiceSection: {
    marginVertical: 8,
  },
  addServiceTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: Colors.TEXT_PRIMARY,
  },
  addServiceForm: {
    gap: 12,
  },
  serviceInput: {
    backgroundColor: "transparent",
  },
  addButton: {
    marginTop: 8,
    borderRadius: 8,
  },
  // Legacy styles (keeping for compatibility)
  addServiceContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 16,
    gap: 8,
  },
  addServiceButton: {
    marginTop: 8,
    minWidth: 80,
  },
  serviceItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: Colors.WHITE,
    marginVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_LIGHT,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceActionButton: {
    minWidth: 60,
  },
});

export default ProfileScreen;
