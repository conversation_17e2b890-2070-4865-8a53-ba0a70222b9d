import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from "react-native";
import { Text, TextInput, ActivityIndicator } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors } from "../../theme";
import { auth } from "../../../config/firebase";
import {
  signInWithEmailAndPassword,
  sendEmailVerification,
} from "firebase/auth";
import {
  signInWithGoogle,
  getGoogleSignInErrorMessage,
} from "../../utils/googleAuthExpo";
import { useLanguage } from "../../context/LanguageContext";

const LoginScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { t } = useLanguage();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert("Lỗi", "Vui lòng nhập đầy đủ thông tin");
      return;
    }

    setIsLoading(true);
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Kiểm tra xem email đã được xác thực chưa
      if (!user.emailVerified) {
        // Gửi lại email xác thực
        await sendEmailVerification(user);
        await auth.signOut();
        Alert.alert(
          "Email chưa được xác thực",
          "Vui lòng kiểm tra email và xác thực tài khoản trước khi đăng nhập. Chúng tôi đã gửi lại email xác thực."
        );
        return;
      }

      console.log("Đăng nhập thành công:", user.email);
      // Không cần navigation.reset() nữa vì RootNavigator sẽ tự động
      // xử lý việc chuyển màn hình dựa trên trạng thái đăng nhập
    } catch (error: any) {
      let errorMessage = "Đã có lỗi xảy ra khi đăng nhập";
      if (error.code === "auth/invalid-email") {
        errorMessage = "Email không hợp lệ";
      } else if (error.code === "auth/user-not-found") {
        errorMessage = "Không tìm thấy tài khoản với email này";
      } else if (error.code === "auth/wrong-password") {
        errorMessage = "Sai mật khẩu";
      }
      Alert.alert("Lỗi", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    try {
      const result = await signInWithGoogle();

      if (result) {
        console.log("Google Sign-In successful:", result.user.email);
        // Navigation will be handled automatically by RootNavigator
      }
    } catch (error: any) {
      console.error("Google Sign-In Error:", error);
      const errorMessage = getGoogleSignInErrorMessage(error, t);
      Alert.alert("Lỗi", errorMessage);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/login.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.title}>{t("auth.welcomeBack")}</Text>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.input}
                placeholder="Nhập email của bạn"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.password")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.passwordPlaceholder")}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
                right={
                  <TextInput.Icon
                    icon={showPassword ? "eye-off" : "eye"}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
              />
            </View>

            <TouchableOpacity
              style={styles.forgotPasswordContainer}
              onPress={() => navigation.navigate("ForgotPassword")}
            >
              <Text style={styles.forgotPasswordText}>
                {t("auth.forgotPassword")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.WHITE} />
              ) : (
                <Text style={styles.loginButtonText}>{t("auth.login")}</Text>
              )}
            </TouchableOpacity>

            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>{t("auth.or")}</Text>
              <View style={styles.divider} />
            </View>

            <TouchableOpacity
              style={styles.googleButton}
              onPress={handleGoogleLogin}
              disabled={isGoogleLoading || isLoading}
            >
              {isGoogleLoading ? (
                <ActivityIndicator color={Colors.TEXT_PRIMARY} />
              ) : (
                <>
                  <Image
                    source={require("../../../assets/icons/google.png")}
                    style={styles.googleIcon}
                  />
                  <Text style={styles.googleButtonText}>
                    {t("auth.signInWithGoogle")}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            <View style={styles.signUpContainer}>
              <Text style={styles.signUpText}>{t("auth.dontHaveAccount")}</Text>
              <TouchableOpacity onPress={() => navigation.navigate("SignUp")}>
                <Text style={styles.signUpLink}>{t("auth.signup")}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 350,
    height: 150,
  },
  formContainer: {
    flex: 1,
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 20,
    paddingTop: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 20,
    marginTop: 10,
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 5,
    marginLeft: 5,
  },
  input: {
    backgroundColor: Colors.WHITE,
  },
  forgotPasswordContainer: {
    alignItems: "flex-end",
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: Colors.PRIMARY,
    fontSize: 14,
    fontWeight: "500",
  },
  loginButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
  },
  loginButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  dividerText: {
    color: Colors.TEXT_SECONDARY,
    paddingHorizontal: 10,
  },
  googleButton: {
    borderWidth: 1,
    borderColor: Colors.GRAY_MEDIUM,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "center",
  },
  googleIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  googleButtonText: {
    color: Colors.TEXT_PRIMARY,
    fontSize: 16,
    fontWeight: "500",
  },
  signUpContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
  },
  signUpText: {
    color: Colors.TEXT_SECONDARY,
    fontSize: 14,
    marginRight: 5,
  },
  signUpLink: {
    color: Colors.PRIMARY,
    fontSize: 14,
    fontWeight: "bold",
  },
});

export default LoginScreen;
