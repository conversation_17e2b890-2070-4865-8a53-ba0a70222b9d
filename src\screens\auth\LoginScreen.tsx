import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from "react-native";
import { Text, TextInput } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors, Spacing, Typography } from "../../theme";
import { useTheme } from "../../context/ThemeContext";
import { GradientBackground } from "../../components/common/GradientBackground";
import { ModernButton } from "../../components/common/ModernButton";
import { ModernCard } from "../../components/common/ModernCard";
import { auth } from "../../../config/firebase";
import {
  signInWithEmailAndPassword,
  sendEmailVerification,
} from "firebase/auth";
import {
  signInWithGoogle,
  getGoogleSignInErrorMessage,
} from "../../utils/googleAuthExpo";
import { useLanguage } from "../../context/LanguageContext";

const LoginScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { t } = useLanguage();
  const { colors } = useTheme();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [error, setError] = useState("");

  const handleLogin = async () => {
    if (!email || !password) {
      setError("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    setIsLoading(true);
    setError("");
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Kiểm tra xem email đã được xác thực chưa
      if (!user.emailVerified) {
        // Gửi lại email xác thực
        await sendEmailVerification(user);
        await auth.signOut();
        Alert.alert(
          "Email chưa được xác thực",
          "Vui lòng kiểm tra email và xác thực tài khoản trước khi đăng nhập. Chúng tôi đã gửi lại email xác thực."
        );
        return;
      }

      console.log("Đăng nhập thành công:", user.email);
      // Không cần navigation.reset() nữa vì RootNavigator sẽ tự động
      // xử lý việc chuyển màn hình dựa trên trạng thái đăng nhập
    } catch (error: any) {
      let errorMessage = "Đã có lỗi xảy ra khi đăng nhập";
      if (error.code === "auth/invalid-email") {
        errorMessage = "Email không hợp lệ";
      } else if (error.code === "auth/user-not-found") {
        errorMessage = "Không tìm thấy tài khoản với email này";
      } else if (error.code === "auth/wrong-password") {
        errorMessage = "Sai mật khẩu";
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    setError("");
    try {
      const result = await signInWithGoogle();

      if (result) {
        console.log("Google Sign-In successful:", result.user.email);
        // Navigation will be handled automatically by RootNavigator
      }
    } catch (error: any) {
      console.error("Google Sign-In Error:", error);
      const errorMessage = getGoogleSignInErrorMessage(error, t);
      setError(errorMessage);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>

        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/welcome.png")}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.welcomeTitle}>{t("auth.welcomeBack")}</Text>
          <Text style={styles.welcomeSubtitle}>
            {t("auth.signInToContinue")}
          </Text>
        </View>
      </SafeAreaView>

      <ModernCard
        style={styles.formContainer}
        variant="elevated"
        borderRadius="xl"
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.scrollView}
        >
          {error ? (
            <ModernCard variant="outlined" style={styles.errorCard}>
              <Text style={styles.errorText}>{error}</Text>
            </ModernCard>
          ) : null}

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập email của bạn"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                setError("");
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              mode="outlined"
              outlineColor={colors.BORDER}
              activeOutlineColor={Colors.PRIMARY}
              textColor={colors.TEXT_PRIMARY}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{t("auth.password")}</Text>
            <TextInput
              style={styles.input}
              placeholder={t("auth.passwordPlaceholder")}
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                setError("");
              }}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              mode="outlined"
              outlineColor={colors.BORDER}
              activeOutlineColor={Colors.PRIMARY}
              textColor={colors.TEXT_PRIMARY}
              right={
                <TextInput.Icon
                  icon={showPassword ? "eye-off" : "eye"}
                  onPress={() => setShowPassword(!showPassword)}
                  color={colors.GRAY_DARK}
                />
              }
            />
          </View>

          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate("ForgotPassword")}
          >
            <Text style={styles.forgotPasswordText}>
              {t("auth.forgotPassword")}
            </Text>
          </TouchableOpacity>

          <ModernButton
            title={t("auth.login")}
            onPress={handleLogin}
            loading={isLoading}
            disabled={isLoading}
            variant="gradient"
            size="large"
            fullWidth
            style={styles.loginButton}
          />

          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.dividerText}>{t("auth.or")}</Text>
            <View style={styles.divider} />
          </View>

          <ModernButton
            title={t("auth.signInWithGoogle")}
            onPress={handleGoogleLogin}
            loading={isGoogleLoading}
            disabled={isGoogleLoading || isLoading}
            variant="outline"
            size="large"
            icon="google"
            fullWidth
            style={styles.googleButton}
          />

          <View style={styles.signUpContainer}>
            <Text style={styles.signUpText}>{t("auth.dontHaveAccount")}</Text>
            <TouchableOpacity onPress={() => navigation.navigate("SignUp")}>
              <Text style={styles.signUpLink}>{t("auth.signup")}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ModernCard>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 0.4,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.SPACING.lg,
  },
  backButton: {
    position: "absolute",
    top: Spacing.SPACING.lg,
    left: Spacing.SPACING.lg,
    width: 44,
    height: 44,
    borderRadius: Spacing.BORDER_RADIUS.round,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    ...Spacing.SHADOW.sm,
  },
  backButtonText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: Spacing.SPACING.xxl,
  },
  logo: {
    width: 200,
    height: 140,
    marginBottom: Spacing.SPACING.lg,
  },
  welcomeTitle: {
    ...Typography.TEXT_STYLES.h2,
    color: Colors.WHITE,
    textAlign: "center",
    marginBottom: Spacing.SPACING.sm,
  },
  welcomeSubtitle: {
    ...Typography.TEXT_STYLES.body,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
  },
  formContainer: {
    flex: 0.6,
    marginTop: -Spacing.SPACING.lg,
    marginHorizontal: Spacing.SPACING.lg,
    marginBottom: Spacing.SPACING.lg,
    padding: 0,
  },
  scrollView: {
    padding: Spacing.SPACING.xl,
  },
  errorCard: {
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderColor: Colors.DANGER,
    marginBottom: Spacing.SPACING.lg,
  },
  errorText: {
    ...Typography.TEXT_STYLES.bodySmall,
    color: Colors.DANGER,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: Spacing.SPACING.lg,
  },
  inputLabel: {
    ...Typography.TEXT_STYLES.bodySmall,
    fontWeight: Typography.FONT_WEIGHT.medium,
    marginBottom: Spacing.SPACING.sm,
    marginLeft: Spacing.SPACING.xs,
  },
  input: {
    backgroundColor: "transparent",
  },
  forgotPasswordContainer: {
    alignItems: "flex-end",
    marginBottom: Spacing.SPACING.xl,
  },
  forgotPasswordText: {
    ...Typography.TEXT_STYLES.bodySmall,
    color: Colors.PRIMARY,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  loginButton: {
    marginBottom: Spacing.SPACING.xl,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: Spacing.SPACING.xl,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  dividerText: {
    ...Typography.TEXT_STYLES.caption,
    marginHorizontal: Spacing.SPACING.lg,
    color: Colors.TEXT_SECONDARY,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  googleButton: {
    marginBottom: Spacing.SPACING.xl,
  },
  signUpContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: Spacing.SPACING.lg,
  },
  signUpText: {
    ...Typography.TEXT_STYLES.body,
    color: Colors.TEXT_SECONDARY,
    marginRight: Spacing.SPACING.xs,
  },
  signUpLink: {
    ...Typography.TEXT_STYLES.body,
    color: Colors.PRIMARY,
    fontWeight: Typography.FONT_WEIGHT.semibold,
  },
});

export default LoginScreen;
