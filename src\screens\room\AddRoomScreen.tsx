import React, { useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import {
  Button,
  Text,
  Chip,
  Surface,
  Icon,
  Divider,
  TouchableRipple,
  Dialog,
  Portal,
  HelperText,
  Paragraph,
  IconButton,
} from "react-native-paper";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import type { RootStackParamList, Room } from "../../types";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";
import { OptimizedTextInput } from "../../components/OptimizedTextInput";
import {
  addRoom,
  updateRoom,
  getRoomsByMotelId,
  setRoomMaintenance,
  addBulkRooms,
} from "../../services/roomService";
import { useMotel } from "../../context/MotelContext";
import { useDefaultPrices } from "../../context/DefaultPricesContext";
import { addLog } from "../../services/logService";
import * as DocumentPicker from "expo-document-picker";
import * as XLSX from "xlsx";
import { addTenant } from "../../services/TenantServices";
import { addBill } from "../../services/BillServices";
import { getTenantsByRoomId } from "../../services/TenantServices";

const ROOM_TYPES = [
  { id: "1", labelKey: "roomTypes.single" },
  { id: "2", labelKey: "roomTypes.double" },
  { id: "3", labelKey: "roomTypes.withLoft" },
  { id: "4", labelKey: "roomTypes.withoutLoft" },
  { id: "5", labelKey: "roomTypes.studio" },
];

const AMENITIES = [
  { id: "1", labelKey: "amenities.airConditioner", icon: "air-conditioner" },
  { id: "2", labelKey: "amenities.refrigerator", icon: "fridge-outline" },
  { id: "3", labelKey: "amenities.washingMachine", icon: "washing-machine" },
  { id: "4", labelKey: "amenities.tv", icon: "television" },
  { id: "5", labelKey: "amenities.kitchen", icon: "stove" },
  { id: "6", labelKey: "amenities.balcony", icon: "window-open-variant" },
  { id: "7", labelKey: "amenities.wifi", icon: "wifi" },
  { id: "8", labelKey: "amenities.hotWater", icon: "water-boiler" },
  { id: "9", labelKey: "amenities.wardrobe", icon: "wardrobe-outline" },
  { id: "10", labelKey: "amenities.bed", icon: "bed-outline" },
];

interface AddRoomScreenProps {
  route?: {
    params?: {
      mode?: "add" | "edit";
      roomData?: Room;
      defaultElectricityPrice?: string;
      defaultWaterPrice?: string;
    };
  };
}

export default function AddRoomScreen({ route }: AddRoomScreenProps) {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { colors, isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useLanguage();
  const { motelId } = useMotel();
  const { defaultElectricityPrice, defaultWaterPrice } = useDefaultPrices();

  const mode = route?.params?.mode || "add";
  const existingRoom = route?.params?.roomData;

  const convertRoomTypesToIds = (loaiPhong: string[] = []) => {
    return loaiPhong
      .map((type) => {
        const roomType = ROOM_TYPES.find((rt) => rt.labelKey === type);
        return roomType ? roomType.id : type;
      })
      .filter(Boolean);
  };

  const convertAmenitiesToIds = (tienNghi: string[] = []) => {
    return tienNghi
      .map((amenity) => {
        const amenityType = AMENITIES.find((a) => a.labelKey === amenity);
        return amenityType ? amenityType.id : amenity;
      })
      .filter(Boolean);
  };

  const [roomData, setRoomData] = useState({
    room_number: existingRoom?.room_number || "",
    area: existingRoom?.dienTich?.toString() || "",
    note: existingRoom?.ghiChu || "",
    price: existingRoom?.gia?.toString() || "",
    electricity_price:
      (existingRoom as any)?.electricity_price?.toString() ||
      defaultElectricityPrice,
    water_price:
      (existingRoom as any)?.water_price?.toString() || defaultWaterPrice,
    status: existingRoom?.status || "available",
    room_types: convertRoomTypesToIds(existingRoom?.loaiPhong),
    amenities: convertAmenitiesToIds(existingRoom?.tienNghi),
  });

  const [bulkAddDialogVisible, setBulkAddDialogVisible] = useState(false);
  const [roomPrefix, setRoomPrefix] = useState("");
  const [roomSuffix, setRoomSuffix] = useState("");
  const [startNumber, setStartNumber] = useState("1");
  const [endNumber, setEndNumber] = useState("10");
  const [previewRooms, setPreviewRooms] = useState<string[]>([]);
  const [confirmBulkAddDialogVisible, setConfirmBulkAddDialogVisible] =
    useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const [expandedSections, setExpandedSections] = useState({
    basicInfo: true,
    pricing: true,
    roomTypes: true,
    amenities: true,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const resetBulkAddForm = () => {
    setRoomPrefix("");
    setRoomSuffix("");
    setStartNumber("1");
    setEndNumber("10");
    setPreviewRooms([]);
    setErrors({});
  };

  const generatePreviewRooms = () => {
    const start = parseInt(startNumber);
    const end = parseInt(endNumber);
    const newErrors: Record<string, string> = {};

    if (isNaN(start) || isNaN(end)) {
      newErrors.range = t("validation.mustBeNumber");
    } else if (start > end) {
      newErrors.range = t("validation.invalidRange");
    } else if (end - start > 100) {
      newErrors.range = t("validation.tooManyRooms");
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      const rooms = [];
      for (let i = start; i <= end; i++) {
        rooms.push(`${roomPrefix}${i}${roomSuffix}`);
      }
      setPreviewRooms(rooms);
    }

    return Object.keys(newErrors).length === 0;
  };

  const validateSingleRoomForm = () => {
    const newErrors: Record<string, string> = {};

    if (!roomData.room_number.trim()) {
      newErrors.roomNumber = t("validation.required");
    }

    if (!roomData.price.trim()) {
      newErrors.price = t("validation.required");
    } else if (isNaN(Number(roomData.price)) || Number(roomData.price) <= 0) {
      newErrors.price = t("validation.invalidPrice");
    }

    if (
      roomData.area.trim() &&
      (isNaN(Number(roomData.area)) || Number(roomData.area) <= 0)
    ) {
      newErrors.area = t("validation.invalidArea");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Hàm kiểm tra số phòng đã tồn tại trong cùng motel
  const checkRoomNumberExists = async (room_number: string) => {
    if (!motelId) return false;
    const rooms = await getRoomsByMotelId(motelId);
    // Nếu đang edit thì bỏ qua chính phòng này
    if (mode === "edit" && existingRoom?.room_id) {
      return rooms.some(
        (r) =>
          r.room_number === room_number && r.room_id !== existingRoom.room_id
      );
    }
    return rooms.some((r) => r.room_number === room_number);
  };

  // Mock update room function
  const handleUpdateRoom = async () => {
    if (!existingRoom?.room_id) {
      Alert.alert(t("common.error"), t("room.updateError"));
      return;
    }
    // Kiểm tra trùng số phòng
    if (await checkRoomNumberExists(roomData.room_number)) {
      const msg =
        t("room.roomNumberExists", { number: roomData.room_number }) ||
        `Số phòng ${roomData.room_number} đã tồn tại`;
      setError(msg);
      Alert.alert(t("common.error"), msg);
      return;
    }
    try {
      setLoading(true);
      setError(null);

      // Lưu trạng thái trước khi cập nhật
      const before = { ...existingRoom };

      // Chuẩn bị dữ liệu cập nhật
      const updatedRoom = {
        room_number: roomData.room_number,
        dienTich: Number(roomData.area) || 0,
        ghiChu: roomData.note || "",
        gia: Number(roomData.price) || 0,
        electricity_price: Number(roomData.electricity_price) || 0,
        water_price: Number(roomData.water_price) || 0,
        status: roomData.status as "available" | "occupied" | "maintenance",
        loaiPhong: roomData.room_types.map(
          (id: any) => ROOM_TYPES.find((type) => type.id === id)?.labelKey || ""
        ),
        tienNghi: roomData.amenities.map(
          (id: any) =>
            AMENITIES.find((amenity) => amenity.id === id)?.labelKey || ""
        ),
        // Các trường khác nếu cần
      };

      const success = await updateRoom(existingRoom.room_id, updatedRoom);
      if (success) {
        // Ghi log thao tác cập nhật phòng
        await addLog({
          action: "edit",
          targetId: existingRoom.room_id,
          targetType: "room",
          motelId: motelId || "",
          before,
          after: updatedRoom,
          description: `Cập nhật phòng: ${updatedRoom.room_number}`,
        });
        Alert.alert(t("common.success"), t("room.updateSuccess"));
        navigation.goBack();
      } else {
        Alert.alert(t("common.error"), t("room.updateError"));
      }
    } catch (error) {
      Alert.alert(t("common.error"), t("room.updateError"));
    } finally {
      setLoading(false);
    }
  };

  const handleSingleRoomSubmit = async () => {
    if (mode === "edit") {
      await handleUpdateRoom();
      return;
    }

    // Kiểm tra trùng số phòng
    if (await checkRoomNumberExists(roomData.room_number)) {
      const msg =
        t("room.roomNumberExists", { number: roomData.room_number }) ||
        `Số phòng ${roomData.room_number} đã tồn tại`;
      setError(msg);
      Alert.alert(t("common.error"), msg);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (!roomData.room_number) {
        setError("Vui lòng nhập số phòng");
        return;
      }

      if (!roomData.price) {
        setError("Vui lòng nhập giá phòng");
        return;
      }

      const newRoom = {
        room_number: roomData.room_number,
        dienTich: Number(roomData.area) || 0,
        ghiChu: roomData.note || "",
        gia: Number(roomData.price) || 0,
        electricity_price: Number(roomData.electricity_price) || 0,
        water_price: Number(roomData.water_price) || 0,
        status: roomData.status as "available" | "occupied" | "maintenance",
        loaiPhong: roomData.room_types.map(
          (id: any) => ROOM_TYPES.find((type) => type.id === id)?.labelKey || ""
        ),
        tienNghi: roomData.amenities.map(
          (id: any) =>
            AMENITIES.find((amenity) => amenity.id === id)?.labelKey || ""
        ),
        motel_id: motelId || "",
        tienCoc: 0,
        tenant_id: null,
        created_at: new Date(),
        start_date: null,
        end_date: null,
        deleted: false,
      };

      const result = await addRoom(newRoom);

      if (result.success) {
        // Ghi log thao tác thêm phòng
        await addLog({
          action: "add",
          targetId: result.id || "",
          targetType: "room",
          motelId: motelId || "",
          after: newRoom,
          description: t("validation.addRoomDescription", {
            roomNumber: newRoom.room_number,
          }),
        });
        navigation.goBack();
      } else {
        setError(t("validation.addRoomError"));
      }
    } catch (err) {
      setError(t("validation.addRoomError"));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkRoomSubmit = () => {
    if (previewRooms.length === 0) {
      if (!generatePreviewRooms()) {
        return;
      }
    }
    setConfirmBulkAddDialogVisible(true);
  };

  const confirmBulkRoomAddition = async () => {
    setConfirmBulkAddDialogVisible(false);
    setBulkAddDialogVisible(false);
    setLoading(true);
    try {
      // Generate room objects from current input
      const start = parseInt(startNumber);
      const end = parseInt(endNumber);
      const rooms = [];
      for (let i = start; i <= end; i++) {
        rooms.push({
          room_number: `${roomPrefix}${i}${roomSuffix}`,
          dienTich: Number(roomData.area) || 0,
          ghiChu: roomData.note || "",
          gia: Number(roomData.price) || 0,
          status: "available" as "available",
          loaiPhong: roomData.room_types.map(
            (id: any) =>
              ROOM_TYPES.find((type) => type.id === id)?.labelKey || ""
          ),
          tienNghi: roomData.amenities.map(
            (id: any) =>
              AMENITIES.find((amenity) => amenity.id === id)?.labelKey || ""
          ),
          motel_id: motelId || "",
          tienCoc: 0,
          tenant_id: null,
          created_at: new Date(),
          start_date: null,
          end_date: null,
        });
      }
      // Call addBulkRooms
      const result = await addBulkRooms(rooms);
      if (result.success) {
        let msg =
          t("room.bulkAddSuccess", { count: result.ids?.length || 0 }) ||
          `Đã thêm ${result.ids?.length || 0} phòng mới!`;
        if (result.duplicated && result.duplicated.length > 0) {
          msg += `\n${
            t("room.bulkAddDuplicated", { count: result.duplicated.length }) ||
            `Bỏ qua ${result.duplicated.length} phòng trùng số.`
          }`;
        }
        Alert.alert(t("common.success"), msg, [
          { text: t("common.done"), onPress: () => navigation.goBack() },
        ]);
      } else {
        Alert.alert(
          t("common.error"),
          t("room.bulkAddError") || "Có lỗi khi thêm nhiều phòng."
        );
      }
    } catch (err) {
      Alert.alert(
        t("common.error"),
        t("room.bulkAddError") || "Có lỗi khi thêm nhiều phòng."
      );
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const toggleRoomType = (id: string) => {
    setRoomData((prev) => ({
      ...prev,
      room_types: prev.room_types.includes(id)
        ? prev.room_types.filter((type) => type !== id)
        : [...prev.room_types, id],
    }));
  };

  const toggleAmenity = (id: string) => {
    setRoomData((prev) => ({
      ...prev,
      amenities: prev.amenities.includes(id)
        ? prev.amenities.filter((amenity) => amenity !== id)
        : [...prev.amenities, id],
    }));
  };

  const renderSection = (
    titleKey: string,
    sectionKey: keyof typeof expandedSections,
    children: React.ReactNode
  ) => (
    <Surface
      style={[styles.section, { backgroundColor: colors.CARD_BACKGROUND }]}
      elevation={1}
    >
      <TouchableRipple onPress={() => toggleSection(sectionKey)}>
        <View style={styles.sectionHeader}>
          <Icon
            source={
              expandedSections[sectionKey] ? "chevron-down" : "chevron-right"
            }
            size={24}
            color="#006eff"
          />
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: "#006eff" }]}
          >
            {t(titleKey)}
          </Text>
        </View>
      </TouchableRipple>
      {expandedSections[sectionKey] && (
        <>
          <Divider
            style={[styles.divider, { backgroundColor: colors.DIVIDER }]}
          />
          <View style={styles.sectionContent}>{children}</View>
        </>
      )}
    </Surface>
  );

  // Hàm import Excel sheet template_phong_tro (fix lỗi typescript)
  const handleImportExcel = async () => {
    try {
      setLoading(true);
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.ms-excel",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });
      if (result.canceled || !result.assets?.[0]?.uri) return;

      const fileUri = result.assets[0].uri;
      const response = await fetch(fileUri);
      const arrayBuffer = await response.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: "array" });
      const sheet = workbook.Sheets["template_phong_tro"];
      if (!sheet) {
        Alert.alert("Lỗi", "Không tìm thấy sheet 'template_phong_tro'");
        return;
      }
      const rows: any[][] = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      if (!rows || rows.length < 4) {
        Alert.alert("Lỗi", "Sheet không đủ dữ liệu");
        return;
      }
      // Tìm dòng header đầu tiên chứa "Mã phòng"
      let headerRowIdx = rows.findIndex(
        (row) =>
          Array.isArray(row) &&
          row.some(
            (cell) =>
              typeof cell === "string" && cell.trim().includes("Mã phòng")
          )
      );
      if (headerRowIdx === -1) {
        Alert.alert("Lỗi", "Không tìm thấy dòng tiêu đề chứa 'Mã phòng'");
        return;
      }
      const header: string[] = rows[headerRowIdx] as string[];
      let successCount = 0;
      // Tạo mapping mã phòng -> room_id
      const roomCodeToId: Record<string, string> = {};
      for (let i = headerRowIdx + 1; i < rows.length; i++) {
        const row = rows[i];
        if (!row || !Array.isArray(row) || row.every((cell) => !cell)) continue;
        const rowObj: Record<string, any> = {};
        header.forEach((key: string, idx: number) => {
          rowObj[key] = row[idx];
        });
        // Ánh xạ sang roomData
        const roomData = {
          room_number: rowObj["Mã phòng"]?.toString() || "",
          dienTich: Number(rowObj["Diện tích (m2)"]) || 0,
          gia: Number(rowObj["Giá thuê"]) || 0,
          electricity_price: Number(rowObj["Giá điện"]) || 0,
          water_price: Number(rowObj["Giá nước"]) || 0,
          ghiChu: rowObj["Ghi chú"] || "",
          tienNghi: rowObj["Tiện ích"]
            ? (rowObj["Tiện ích"] as string)
                .split(",")
                .map((s: string) => s.trim())
            : [],
          loaiPhong: [],
          status: "available" as "available",
          motel_id: motelId || "",
          tienCoc: 0,
          tenant_id: null,
          created_at: new Date(),
          start_date: null,
          end_date: null,
          deleted: false,
        };
        // Debug: log từng dòng ánh xạ trước khi gọi addRoom
        console.log("[IMPORT DEBUG] rowObj:", rowObj);
        console.log("[IMPORT DEBUG] roomData:", roomData);
        // Gọi addRoom thật sự
        const result = await addRoom(roomData);
        console.log("[IMPORT][ROOM] result:", result);
        if (result.success) {
          successCount++;
          // Lưu mapping mã phòng -> room_id
          console.log(
            "[IMPORT][ROOM][MAPPING]",
            roomData.room_number,
            result.id
          );
          if (roomData.room_number && result.id) {
            roomCodeToId[roomData.room_number] = result.id;
          }
        }
      }
      Alert.alert(
        t("common.success"),
        t("validation.importSuccess", { count: successCount })
      );
      console.log("[IMPORT][ROOM][roomCodeToId]", roomCodeToId);
      // ===== IMPORT SHEET template_nguoi_thue =====
      // Tạo mapping tenantKeyToId: { room_id|tenant_name: tenant_id }
      const tenantKeyToId: Record<string, string> = {};
      const sheetTenant = workbook.Sheets["template_nguoi_thue"];
      if (sheetTenant) {
        const rowsTenant: any[][] = XLSX.utils.sheet_to_json(sheetTenant, {
          header: 1,
        });
        if (!rowsTenant || rowsTenant.length < 2) {
          console.log(
            "[IMPORT DEBUG] Sheet template_nguoi_thue không đủ dữ liệu"
          );
        } else {
          // Tìm dòng header chứa "Tên người thuê"
          let headerRowIdxTenant = rowsTenant.findIndex(
            (row) =>
              Array.isArray(row) &&
              row.some(
                (cell) =>
                  typeof cell === "string" &&
                  cell.trim().includes("Tên người thuê")
              )
          );
          if (headerRowIdxTenant === -1) {
            console.log(
              "[IMPORT DEBUG] Không tìm thấy dòng tiêu đề chứa 'Tên người thuê'"
            );
          } else {
            const headerTenant: string[] = rowsTenant[
              headerRowIdxTenant
            ] as string[];
            let successTenant = 0;
            for (let i = headerRowIdxTenant + 1; i < rowsTenant.length; i++) {
              const row = rowsTenant[i];
              if (!row || !Array.isArray(row) || row.every((cell) => !cell))
                continue;
              const rowObj: Record<string, any> = {};
              headerTenant.forEach((key: string, idx: number) => {
                rowObj[key] = row[idx];
              });
              // Tìm key trường chủ hộ/phòng trong header (chỉ cần chứa 'chủ', không cần 'true')
              const ownerKey = headerTenant.find(
                (k) =>
                  typeof k === "string" &&
                  removeVietnameseTones(k).includes("chu")
              );
              // Ánh xạ sang tenantData
              const room_code = rowObj["Mã phòng"]?.toString() || "";
              let normalized_room_code = room_code
                .replace(/^Phòng\s*/i, "")
                .trim();
              const room_id =
                roomCodeToId[normalized_room_code] ||
                roomCodeToId[room_code] ||
                roomCodeToId[room_code.trim()];
              if (!room_id) {
                console.warn(
                  `[IMPORT] Không tìm thấy room_id cho mã phòng: ${room_code}`
                );
                continue;
              }
              const is_owner_raw = ownerKey ? rowObj[ownerKey] : undefined;
              let is_main_tenant = false;
              if (is_owner_raw !== undefined && is_owner_raw !== null) {
                const raw = is_owner_raw.toString().trim().toLowerCase();
                let base64 = "";
                try {
                  base64 =
                    typeof Buffer !== "undefined"
                      ? Buffer.from(raw).toString("base64")
                      : btoa(unescape(encodeURIComponent(raw)));
                } catch (e) {
                  base64 = "err";
                }
                const rawNoSign = removeVietnameseTones(raw);
                is_main_tenant =
                  raw === "true" ||
                  raw === "1" ||
                  raw === "yes" ||
                  raw === "x" ||
                  rawNoSign === "co" ||
                  rawNoSign === "owner" ||
                  rawNoSign === "chuho" ||
                  rawNoSign === "chuchinh" ||
                  rawNoSign === "chuphong";
              }
              const tenantData = {
                name: rowObj["Tên người thuê"]?.toString() || "",
                phone: rowObj["Số điện thoại"]?.toString() || "",
                identity_number: rowObj["Số CMND/CCCD"]?.toString() || "",
                room_id,
                is_main_tenant,
                note: rowObj["Ghi chú"]?.toString() || "",
              };
              try {
                const resultTenant = await addTenant(tenantData);
                if (resultTenant.success) {
                  successTenant++;
                  // Mapping tenantKeyToId: key = room_id|tenant_name (không dấu, lowercase, trim)
                  const key = `${room_id}|${removeVietnameseTones(
                    tenantData.name
                  )
                    .toLowerCase()
                    .trim()}`;
                  if (resultTenant.id) tenantKeyToId[key] = resultTenant.id;
                  // Nếu là chủ phòng, cập nhật status phòng thành 'occupied'
                  if (is_main_tenant && room_id) {
                    try {
                      await updateRoom(room_id, { status: "occupied" });
                    } catch (e) {
                      console.warn(
                        `[IMPORT][ROOM][UPDATE] Failed to set room ${room_id} status:`,
                        e
                      );
                    }
                  }
                } else
                  console.warn(
                    "[IMPORT][TENANT] addTenant failed:",
                    resultTenant.error
                  );
              } catch (e) {
                console.error("[IMPORT][TENANT] addTenant exception:", e);
              }
            }
            Alert.alert(
              "Thành công",
              `Đã import xong ${successTenant} người thuê từ file Excel!`
            );
          }
        }
      }
      // ===== IMPORT SHEET template_hoa_don =====
      const sheetBill = workbook.Sheets["template_hoa_don"];
      if (sheetBill) {
        const rowsBill: any[][] = XLSX.utils.sheet_to_json(sheetBill, {
          header: 1,
        });
        if (!rowsBill || rowsBill.length < 2) {
          console.log("[IMPORT DEBUG] Sheet template_hoa_don không đủ dữ liệu");
        } else {
          let headerRowIdxBill = rowsBill.findIndex(
            (row) =>
              Array.isArray(row) &&
              row.some(
                (cell) =>
                  typeof cell === "string" && cell.trim().includes("Mã phòng")
              )
          );
          if (headerRowIdxBill === -1) {
            console.log(
              "[IMPORT DEBUG] Không tìm thấy dòng tiêu đề chứa 'Mã phòng'"
            );
          } else {
            const headerBill: string[] = rowsBill[headerRowIdxBill] as string[];
            let successBill = 0;
            for (let i = headerRowIdxBill + 1; i < rowsBill.length; i++) {
              const row = rowsBill[i];
              if (!row || !Array.isArray(row) || row.every((cell) => !cell))
                continue;
              const rowObj: Record<string, any> = {};
              headerBill.forEach((key: string, idx: number) => {
                rowObj[key] = row[idx];
              });
              // Mapping room_code sang room_id
              const room_code = rowObj["Mã phòng"]?.toString() || "";
              let normalized_room_code = room_code
                .replace(/^Phòng\s*/i, "")
                .trim();
              const room_id =
                roomCodeToId[normalized_room_code] ||
                roomCodeToId[room_code] ||
                roomCodeToId[room_code.trim()];
              if (!room_id) {
                console.warn(
                  `[IMPORT][BILL] Không tìm thấy room_id cho mã phòng: ${room_code}`
                );
                continue;
              }
              // Mapping các trường hóa đơn
              if (!motelId) {
                console.warn(
                  `[IMPORT][BILL] Thiếu motelId khi import hóa đơn cho phòng: ${room_code}`
                );
                continue; // Bỏ qua hóa đơn này nếu không có motelId
              }
              // Chuẩn hóa trường tháng hóa đơn (có ngày, định dạng DD/MM/YYYY)
              let rawThangHoaDon = rowObj["Tháng tạo hóa đơn"];
              let thang_hoa_don = "";
              if (typeof rawThangHoaDon === "number") {
                // Excel date serial -> JS date
                const excelEpoch = new Date(1899, 11, 30);
                const billDate = new Date(
                  excelEpoch.getTime() + rawThangHoaDon * 24 * 60 * 60 * 1000
                );
                // Lấy ngày/tháng/năm từ billDate
                const day = billDate.getDate().toString().padStart(2, "0");
                const month = (billDate.getMonth() + 1)
                  .toString()
                  .padStart(2, "0");
                const year = billDate.getFullYear();
                thang_hoa_don = `${day}/${month}/${year}`;
              } else if (typeof rawThangHoaDon === "string") {
                // Tìm các dạng MM/YYYY, YYYY-MM, MM-YYYY, YYYY/MM
                let match = rawThangHoaDon.match(/(\d{1,2})[\/-](\d{4})/);
                if (match) {
                  // Nếu chỉ có tháng/năm thì lấy ngày là 01
                  thang_hoa_don = `01/${match[1].padStart(2, "0")}/${match[2]}`;
                } else {
                  // Nếu là dạng YYYY-MM-DD hoặc DD/MM/YYYY thì cố gắng parse lại
                  match = rawThangHoaDon.match(
                    /(\d{4})[\/-](\d{1,2})[\/-](\d{1,2})/
                  );
                  if (match) {
                    thang_hoa_don = `${match[3].padStart(
                      2,
                      "0"
                    )}/${match[2].padStart(2, "0")}/${match[1]}`;
                  } else {
                    // Nếu là dạng DD/MM/YYYY thì giữ nguyên
                    match = rawThangHoaDon.match(
                      /(\d{1,2})[\/-](\d{1,2})[\/-](\d{4})/
                    );
                    if (match) {
                      thang_hoa_don = `${match[1].padStart(
                        2,
                        "0"
                      )}/${match[2].padStart(2, "0")}/${match[3]}`;
                    } else {
                      thang_hoa_don = rawThangHoaDon;
                    }
                  }
                }
              } else {
                thang_hoa_don = "";
              }
              const billData: any = {
                phong_id: room_id,
                motel_id: motelId, // luôn truyền đúng motelId, không phải ""
                tenant_name: rowObj["Tên người thuê"]?.toString() || "",
                thang_hoa_don,
                tienphong: Number(rowObj["Tiền phòng"]) || 0,
                tiendien: Number(rowObj["Tiền điện"]) || 0,
                tiennuoc: Number(rowObj["Tiền nước"]) || 0,
                giadien: Number(rowObj["Giá điện"]) || 0,
                gianuoc: Number(rowObj["Giá nước"]) || 0,
                additional_fees: rowObj["Phí khác"]
                  ? [{ name: "Phí khác", amount: Number(rowObj["Phí khác"]) }]
                  : [],
                tong_tien: Number(rowObj["Tổng tiền"]) || 0,
                chi_so_dien_cu: Number(rowObj["Chỉ số điện cũ"]) || 0,
                chi_so_dien_moi: Number(rowObj["Chỉ số điện mới"]) || 0,
                chi_so_nuoc_cu: Number(rowObj["Chỉ số nước cũ"]) || 0,
                chi_so_nuoc_moi: Number(rowObj["Chỉ số nước mới"]) || 0,
                payment_status:
                  rowObj["Trạng thái thanh toán"]?.toString().toLowerCase() ===
                    "đã thanh toán" ||
                  rowObj["Trạng thái thanh toán"]?.toString().toLowerCase() ===
                    "paid"
                    ? "paid"
                    : "pending",
              };
              // Mapping tenant_id nếu có
              // Giả sử bạn có tenantKeyToId mapping (room_id|tenant_name -> tenant_id)
              let tenant_id = "";
              if (typeof tenantKeyToId !== "undefined") {
                const key = `${room_id}|${removeVietnameseTones(
                  billData.tenant_name
                )
                  .toLowerCase()
                  .trim()}`;
                tenant_id = tenantKeyToId[key];
              }
              // Nếu không có tenant_id thì để là ""
              billData.tenant_id = tenant_id || "";
              // Gọi hàm tạo hóa đơn
              try {
                const resultBill = await addBill(billData);
                if (resultBill.success) successBill++;
                else
                  console.warn(
                    "[IMPORT][BILL] addBill failed:",
                    resultBill.error
                  );
              } catch (e) {
                console.error("[IMPORT][BILL] addBill exception:", e);
              }
            }
            Alert.alert(
              "Thành công",
              `Đã import xong ${successBill} hóa đơn từ file Excel!`
            );
          }
        }
      }
    } catch (err) {
      Alert.alert("Lỗi", "Không thể import file Excel");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Xử lý chuyển trạng thái phòng (bảo trì/ngưng bảo trì)
  const handleToggleMaintenance = async () => {
    if (!existingRoom?.room_id) return;
    const isMaintenance = roomData.status === "maintenance";
    // Nếu chuyển sang bảo trì, kiểm tra phòng không có tenant
    if (!isMaintenance) {
      const tenants = await getTenantsByRoomId(existingRoom.room_id);
      if (tenants && tenants.length > 0) {
        Alert.alert(
          t("common.error"),
          t("room.cannotSetMaintenanceWithTenants") ||
            "Không thể bảo trì phòng đang có người ở. Vui lòng chuyển hết người ra khỏi phòng trước khi bảo trì."
        );
        return;
      }
    }
    Alert.alert(
      isMaintenance
        ? t("room.stopMaintenanceConfirm") || "Ngưng bảo trì phòng này?"
        : t("room.maintenanceConfirm") ||
            "Chuyển phòng sang trạng thái bảo trì?",
      isMaintenance
        ? t("room.stopMaintenanceDesc") ||
            "Phòng sẽ trở lại trạng thái sẵn sàng cho thuê."
        : t("room.maintenanceDesc") ||
            "Phòng sẽ không cho thuê trong thời gian bảo trì.",
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: isMaintenance
            ? t("room.stopMaintenance") || "Ngưng bảo trì"
            : t("room.statusMaintenance") || "Bảo trì phòng",
          style: "destructive",
          onPress: async () => {
            setLoading(true);
            try {
              const ok = await setRoomMaintenance(
                existingRoom.room_id,
                !isMaintenance
              );
              if (ok) {
                setRoomData((prev) => ({
                  ...prev,
                  status: isMaintenance ? "available" : "maintenance",
                }));
                Alert.alert(
                  t("common.success"),
                  isMaintenance
                    ? t("room.stopMaintenanceSuccess") || "Đã ngưng bảo trì."
                    : t("room.maintenanceSuccess") || "Đã chuyển sang bảo trì."
                );
                navigation.goBack();
              } else {
                Alert.alert(t("common.error"), t("room.updateError"));
              }
            } catch (e) {
              Alert.alert(t("common.error"), t("room.updateError"));
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.BACKGROUND }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <Surface
        style={[styles.header, { backgroundColor: "#006eff" }]}
        elevation={2}
      >
        <Text variant="headlineSmall" style={{ color: colors.WHITE }}>
          {mode === "edit" ? t("room.editRoom") : t("room.addRoom")}
        </Text>
      </Surface>

      <ScrollView style={styles.form}>
        {renderSection(
          "room.roomInfo",
          "basicInfo",
          <>
            <OptimizedTextInput
              mode="outlined"
              label={t("room.roomNumber")}
              placeholder={t("room.roomNumberPlaceholder")}
              value={roomData.room_number}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, room_number: text })
              }
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              leftIcon="door"
              error={!!errors.roomNumber}
            />
            {errors.roomNumber && (
              <HelperText type="error">{errors.roomNumber}</HelperText>
            )}

            <OptimizedTextInput
              mode="outlined"
              label={t("room.areaWithUnit")}
              placeholder={t("room.areaPlaceholder")}
              value={roomData.area}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, area: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              leftIcon="ruler-square"
              error={!!errors.area}
            />
            {errors.area && <HelperText type="error">{errors.area}</HelperText>}

            <OptimizedTextInput
              mode="outlined"
              label={t("room.notes")}
              placeholder={t("room.notesPlaceholder")}
              value={roomData.note}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, note: text })
              }
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              multiline
              numberOfLines={3}
              leftIcon="note-text"
            />
          </>
        )}

        {renderSection(
          "room.pricingAndServices",
          "pricing",
          <>
            <OptimizedTextInput
              mode="outlined"
              label={t("room.roomPriceVND")}
              placeholder={t("room.pricePlaceholder")}
              value={roomData.price}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, price: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              leftIcon="cash"
              error={!!errors.price}
            />
            {errors.price && (
              <HelperText type="error">{errors.price}</HelperText>
            )}

            <OptimizedTextInput
              mode="outlined"
              label={t("room.electricityPriceUnit")}
              value={roomData.electricity_price}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, electricity_price: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              leftIcon="flash"
            />

            <OptimizedTextInput
              mode="outlined"
              label={t("room.waterPriceUnit")}
              value={roomData.water_price}
              onChangeText={(text: string) =>
                setRoomData({ ...roomData, water_price: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              leftIcon="water"
            />
          </>
        )}

        {renderSection(
          "room.roomType",
          "roomTypes",
          <View style={styles.chipContainer}>
            {ROOM_TYPES.map((type) => (
              <Chip
                key={type.id}
                selected={roomData.room_types.includes(type.id)}
                onPress={() => toggleRoomType(type.id)}
                style={[
                  styles.chip,
                  {
                    backgroundColor: isDarkMode ? colors.GRAY_LIGHT : "#F0F0F0",
                  },
                  roomData.room_types.includes(type.id) && {
                    backgroundColor: "#006eff",
                  },
                ]}
                textStyle={[
                  { color: colors.TEXT_PRIMARY },
                  roomData.room_types.includes(type.id) && {
                    color: colors.WHITE,
                  },
                ]}
                showSelectedCheck
                icon="home-variant"
              >
                {t(type.labelKey)}
              </Chip>
            ))}
          </View>
        )}

        {renderSection(
          "room.amenities",
          "amenities",
          <View style={styles.amenitiesGrid}>
            {AMENITIES.map((amenity) => (
              <Chip
                key={amenity.id}
                selected={roomData.amenities.includes(amenity.id)}
                onPress={() => toggleAmenity(amenity.id)}
                style={[
                  styles.amenityChip,
                  {
                    backgroundColor: isDarkMode ? colors.GRAY_LIGHT : "#F0F0F0",
                  },
                  roomData.amenities.includes(amenity.id) && {
                    backgroundColor: "#006eff",
                  },
                ]}
                textStyle={[
                  { color: colors.TEXT_PRIMARY },
                  roomData.amenities.includes(amenity.id) && {
                    color: colors.WHITE,
                  },
                ]}
                showSelectedCheck
                icon={amenity.icon}
              >
                {t(amenity.labelKey)}
              </Chip>
            ))}
          </View>
        )}

        <Surface
          style={[
            styles.bulkAddContainer,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={1}
        >
          <View style={styles.bulkAddHeader}>
            <View>
              <Text style={[styles.bulkAddTitle, { color: "#006eff" }]}>
                {t("room.smartAdd")}
              </Text>
              <Text style={{ color: colors.TEXT_SECONDARY }}>
                {t("room.smartAddDescription")}
              </Text>
            </View>
            <IconButton
              icon="plus-box-multiple"
              size={24}
              iconColor="#006eff"
              onPress={() => {
                resetBulkAddForm();
                setBulkAddDialogVisible(true);
              }}
            />
          </View>
        </Surface>
        {/* Dialog cấu hình thêm nhiều phòng */}
        <Portal>
          <Dialog
            visible={bulkAddDialogVisible}
            onDismiss={() => setBulkAddDialogVisible(false)}
            style={{
              backgroundColor: colors.CARD_BACKGROUND,
              maxHeight: "90%",
            }}
          >
            <ScrollView>
              <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
                {t("room.configureSmartAdd")}
              </Dialog.Title>
              <Dialog.Content>
                <Paragraph
                  style={{ color: colors.TEXT_SECONDARY, marginBottom: 16 }}
                >
                  {t("room.bulkCreationDescription")}
                </Paragraph>

                <OptimizedTextInput
                  mode="outlined"
                  label={t("room.roomPrefix")}
                  value={roomPrefix}
                  onChangeText={setRoomPrefix}
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                  placeholder="A-"
                />

                <OptimizedTextInput
                  mode="outlined"
                  label={t("room.roomSuffix")}
                  value={roomSuffix}
                  onChangeText={setRoomSuffix}
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                />

                <View style={styles.formRow}>
                  <View style={styles.formColumn}>
                    <OptimizedTextInput
                      mode="outlined"
                      label={t("room.startNumber")}
                      value={startNumber}
                      onChangeText={setStartNumber}
                      keyboardType="numeric"
                      style={[
                        styles.input,
                        { backgroundColor: colors.CARD_BACKGROUND },
                      ]}
                    />
                  </View>

                  <View style={styles.formColumn}>
                    <OptimizedTextInput
                      mode="outlined"
                      label={t("room.endNumber")}
                      value={endNumber}
                      onChangeText={setEndNumber}
                      keyboardType="numeric"
                      style={[
                        styles.input,
                        { backgroundColor: colors.CARD_BACKGROUND },
                      ]}
                    />
                  </View>
                </View>

                {errors.range && (
                  <HelperText type="error">{errors.range}</HelperText>
                )}

                <Button
                  mode="contained"
                  onPress={generatePreviewRooms}
                  style={{
                    marginTop: 8,
                    backgroundColor: "#006eff",
                    marginBottom: 16,
                  }}
                >
                  {t("room.previewRooms")}
                </Button>

                {previewRooms.length > 0 && (
                  <View style={{ marginTop: 8 }}>
                    <Text
                      style={{
                        color: colors.TEXT_PRIMARY,
                        fontWeight: "bold",
                        marginBottom: 8,
                      }}
                    >
                      {t("room.roomsToCreate")} {previewRooms.length}
                    </Text>
                    <View style={styles.chipContainer}>
                      {previewRooms.slice(0, 10).map((room, index) => (
                        <Chip
                          key={index}
                          style={[
                            styles.chip,
                            {
                              backgroundColor: isDarkMode
                                ? colors.GRAY_LIGHT
                                : "#F0F0F0",
                            },
                          ]}
                        >
                          {room}
                        </Chip>
                      ))}
                      {previewRooms.length > 10 && (
                        <Chip
                          style={[
                            styles.chip,
                            {
                              backgroundColor: isDarkMode
                                ? colors.GRAY_LIGHT
                                : "#F0F0F0",
                            },
                          ]}
                        >
                          +{previewRooms.length - 10} {t("room.rooms")}
                        </Chip>
                      )}
                    </View>
                  </View>
                )}
              </Dialog.Content>
              <Dialog.Actions>
                <Button onPress={() => setBulkAddDialogVisible(false)}>
                  {t("common.cancel")}
                </Button>
                <Button
                  onPress={handleBulkRoomSubmit}
                  mode="contained"
                  style={{ backgroundColor: "#006eff" }}
                  labelStyle={{ color: "#ffffff" }}
                  disabled={previewRooms.length === 0}
                >
                  {t("room.addMultipleRooms")}
                </Button>
              </Dialog.Actions>
            </ScrollView>
          </Dialog>
        </Portal>
        {/* Dialog xác nhận thêm nhiều phòng */}
        <Portal>
          <Dialog
            visible={confirmBulkAddDialogVisible}
            onDismiss={() => setConfirmBulkAddDialogVisible(false)}
            style={{ backgroundColor: colors.CARD_BACKGROUND }}
          >
            <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
              {t("common.confirm")}
            </Dialog.Title>
            <Dialog.Content>
              <Paragraph style={{ color: colors.TEXT_SECONDARY }}>
                {t("room.bulkAddConfirmation")} {previewRooms.length}
              </Paragraph>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setConfirmBulkAddDialogVisible(false)}>
                {t("common.cancel")}
              </Button>
              <Button
                onPress={confirmBulkRoomAddition}
                mode="contained"
                style={{ backgroundColor: "#006eff" }}
              >
                {t("common.confirm")}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
        <Button
          mode="outlined"
          icon="file-excel"
          onPress={handleImportExcel}
          style={{ marginBottom: 12, borderColor: "#006eff" }}
        >
          {t("room.importFromExcel") || "Nhập từ Excel"}
        </Button>
        <View style={styles.buttonContainer}>
          {/* Nút bảo trì/ngưng bảo trì phòng chỉ hiển thị khi edit */}
          {mode === "edit" && (
            <Button
              mode="outlined"
              onPress={handleToggleMaintenance}
              style={[
                styles.cancelButton,
                {
                  borderColor:
                    roomData.status === "maintenance" ? "#4caf50" : "#ff9800",
                },
              ]}
              icon={
                roomData.status === "maintenance" ? "check-circle" : "tools"
              }
            >
              {roomData.status === "maintenance"
                ? t("room.stopMaintenance") || "Ngưng bảo trì"
                : t("room.statusMaintenance") || "Bảo trì phòng"}
            </Button>
          )}

          <Button
            mode="contained"
            onPress={handleSingleRoomSubmit}
            style={[styles.submitButton, { backgroundColor: "#006eff" }]}
            icon="check-circle"
          >
            {mode === "edit" ? t("common.update") : t("room.addRoomAction")}
          </Button>

          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.cancelButton, { borderColor: "#006eff" }]}
            icon="close-circle"
          >
            {t("common.cancel")}
          </Button>
        </View>
      </ScrollView>

      {/* Bỏ dialog thêm nhiều phòng */}
      {/*
      <Portal>
        <Dialog
          visible={bulkAddDialogVisible}
          onDismiss={() => setBulkAddDialogVisible(false)}
          style={{ backgroundColor: colors.CARD_BACKGROUND }}
        >
          <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
            {t("room.configureSmartAdd")}
          </Dialog.Title>
          <Dialog.Content>
            <Paragraph
              style={{ color: colors.TEXT_SECONDARY, marginBottom: 16 }}
            >
              {t("room.bulkCreationDescription")}
            </Paragraph>

            <OptimizedTextInput
              mode="outlined"
              label={t("room.roomPrefix")}
              value={roomPrefix}
              onChangeText={setRoomPrefix}
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              placeholder="A-"
            />

            <OptimizedTextInput
              mode="outlined"
              label={t("room.roomSuffix")}
              value={roomSuffix}
              onChangeText={setRoomSuffix}
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
            />

            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <OptimizedTextInput
                  mode="outlined"
                  label={t("room.startNumber")}
                  value={startNumber}
                  onChangeText={setStartNumber}
                  keyboardType="numeric"
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                />
              </View>

              <View style={styles.formColumn}>
                <OptimizedTextInput
                  mode="outlined"
                  label={t("room.endNumber")}
                  value={endNumber}
                  onChangeText={setEndNumber}
                  keyboardType="numeric"
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                />
              </View>
            </View>

            {errors.range && (
              <HelperText type="error">{errors.range}</HelperText>
            )}

            <Button
              mode="contained"
              onPress={generatePreviewRooms}
              style={{
                marginTop: 8,
                backgroundColor: "#006eff",
                marginBottom: 16,
              }}
            >
              {t("room.previewRooms")}
            </Button>

            {previewRooms.length > 0 && (
              <View style={{ marginTop: 8 }}>
                <Text
                  style={{
                    color: colors.TEXT_PRIMARY,
                    fontWeight: "bold",
                    marginBottom: 8,
                  }}
                >
                  {t("room.roomsToCreate")} {previewRooms.length}
                </Text>
                <View style={styles.chipContainer}>
                  {previewRooms.slice(0, 10).map((room, index) => (
                    <Chip
                      key={index}
                      style={[
                        styles.chip,
                        {
                          backgroundColor: isDarkMode
                            ? colors.GRAY_LIGHT
                            : "#F0F0F0",
                        },
                      ]}
                    >
                      {room}
                    </Chip>
                  ))}
                  {previewRooms.length > 10 && (
                    <Chip
                      style={[
                        styles.chip,
                        {
                          backgroundColor: isDarkMode
                            ? colors.GRAY_LIGHT
                            : "#F0F0F0",
                        },
                      ]}
                    >
                      +{previewRooms.length - 10} {t("room.rooms")}
                    </Chip>
                  )}
                </View>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setBulkAddDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              onPress={handleBulkRoomSubmit}
              mode="contained"
              style={{ backgroundColor: "#006eff" }}
              labelStyle={{ color: "#ffffff" }}
              disabled={previewRooms.length === 0}
            >
              {t("room.addMultipleRooms")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      */}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
  },
  form: {
    padding: 16,
    paddingTop: 24,
  },
  section: {
    marginBottom: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingBottom: 16,
  },
  sectionContent: {
    padding: 16,
    paddingTop: 0,
  },
  sectionTitle: {
    fontWeight: "bold",
    marginLeft: 8,
  },
  divider: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  selectedChip: {
    backgroundColor: "#006eff",
  },
  selectedChipText: {
    color: "#FFFFFF",
  },
  amenitiesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  amenityChip: {
    marginBottom: 8,
  },
  buttonContainer: {
    gap: 12,
    marginTop: 8,
    marginBottom: 24,
  },
  submitButton: {
    padding: 8,
  },
  cancelButton: {
    borderColor: "#006eff",
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  formColumn: {
    flex: 1,
    marginRight: 8,
  },
  bulkAddContainer: {
    padding: 16,
    marginBottom: 16,
    marginHorizontal: 4,
    borderRadius: 12,
  },
  bulkAddHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  bulkAddTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
});

// Hàm loại bỏ dấu tiếng Việt
function removeVietnameseTones(str: string) {
  return str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/đ/g, "d")
    .replace(/Đ/g, "D")
    .replace(/\s+/g, "")
    .toLowerCase();
}
