# Theo <PERSON>õi Tiến Độ
## Nhiệm Vụ Ưu Tiên
### Phát Triển Backend
- [ ] M<PERSON>n hình RoomDetails
- [ ] Màn hinh Report
- [ ] Tính năng Quản lý <PERSON>ồ sơ
  - [ ] Chức năng đổi mật khẩu
  - [ ] Chuyển đổi giữa các tòa nhà/nhà trọ


### Phát Triển Frontend
- [*] Màn hình CRUD RoomDetails
  - [*] Thông tin phòng
    - **Vấn đề với các loại phòng và tiện nghi (Chỉ cần sửa UI lại)**
    - **Thiếu chỗ điền giá điện nước (gi<PERSON> điện nước sẽ điền vào chỗ khách thuê), giá trị mặc đinh của toàn bộ dãy sẽ được điền vào đó trước**
    - **Gi<PERSON> điện nước cho toàn bộ dãy**
  - [*] Người thuê
  - [ ] H<PERSON>a đơn
    - **Vấn đề với một số button**
    - **<PERSON>hi tạo hoá đơn sẽ thêm trường số điện số nước (Sau đó sẽ trừ đi cho tháng trước, sẽ có UI để hiển thị số điện/nước tháng trước)**
- [ ] Giao diện Quản lý Tòa nhà (Sau khi hoàn thành CRUD RoomDetails)
  - [ ] Thao tác CRUD cho tòa nhà/nhà trọ
    - **Cần thêm giá trị mặc định giá điện nước (ở mục Profile)**
    - **Cần thêm dịch vụ (ở mục Profile)**

Thứ tự các việc ưu tiên làm:
1. 
**- CURD cho toà nhà, thống nhất database từ Motels -> Rooms -> tenant**
- Màn hình thống kê, cho các phòng (tổng quan các phòng), có thể sẽ cần sửa UI, bỏ các lầu đi vì không khớp với database (Có thể nâng cấp sau)
2. Giá điện nước cho toàn bộ toà nhà
3. Sửa UI của thêm khách thuê, khi tick là chủ phòng sẽ hiện thêm giá điện, nước kèm thêm tiền cọc (Thêm logic database để kiểm tra chỉ cho một người là chủ phòng)
4. Sửa UI cho hoá đơn (Cần có hoá đơn sau đó mới có data để có thể test sang thống kê về doanh thu)



## Tính Năng Tương Lai
Các tính năng sau sẽ được triển khai sau khi hoàn thành các nhiệm vụ ưu tiên ở trên:
- [*] Triển khai OAuth

- [ ] Nhập Dữ liệu Thông minh
  - Chức năng nhập file CSV
  - **Có thể** tích hợp Google Form.

- [ ] Xem log cho owner

## Vấn đề cần khắc phục
- Check lại data chuyển từ HomeScreen sang RoomDetails
- Chuyển config/firebase.ts vào .env trước khi build file apk để publish


# CURD cho toà nhà, thống nhất database từ Motels -> Rooms -> tenant
Flow 1: Khi login sẽ cho tạo một dãy trọ.
*Theo flow này -> Flow 2: Tạo sẵn một dãy trọ mặc định, sau đó sẽ cho chỉnh sửa sau*

# CURD cho hoá đơn
- Chỗ Update thu tiền bị lỗi về mặt pop up 1 chút. Chắc nhờ anh Trí sửa

# R cho phần thống kê

# Đổi mật khẩu

# Thiết lập giá trị mặc định

# UR cho hồ sơ