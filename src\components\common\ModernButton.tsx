import React from "react";
import {
  TouchableOpacity,
  Text,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientBackground } from "./GradientBackground";
import { useTheme } from "../../context/ThemeContext";
import { BORDER_RADIUS, SPACING, SHADOW } from "../../theme/spacing";
import { TEXT_STYLES } from "../../theme/typography";
import { PRIMARY, WHITE } from "../../theme/colors";

interface ModernButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost" | "gradient";
  size?: "small" | "medium" | "large";
  icon?: string;
  iconPosition?: "left" | "right";
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

export const ModernButton: React.FC<ModernButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  icon,
  iconPosition = "left",
  loading = false,
  disabled = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const { colors, isDarkMode } = useTheme();

  const getSizeStyles = () => {
    switch (size) {
      case "small":
        return {
          paddingHorizontal: SPACING.md,
          paddingVertical: SPACING.sm,
          minHeight: 36,
        };
      case "large":
        return {
          paddingHorizontal: SPACING.xl,
          paddingVertical: SPACING.lg,
          minHeight: 56,
        };
      default:
        return {
          paddingHorizontal: SPACING.lg,
          paddingVertical: SPACING.md,
          minHeight: 48,
        };
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "secondary":
        return {
          backgroundColor: colors.GRAY_LIGHT,
          borderWidth: 0,
        };
      case "outline":
        return {
          backgroundColor: "transparent",
          borderWidth: 2,
          borderColor: PRIMARY,
        };
      case "ghost":
        return {
          backgroundColor: "transparent",
          borderWidth: 0,
        };
      case "gradient":
        return {
          backgroundColor: "transparent",
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: PRIMARY,
          borderWidth: 0,
        };
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case "secondary":
        return colors.TEXT_PRIMARY;
      case "outline":
        return PRIMARY;
      case "ghost":
        return PRIMARY;
      case "gradient":
        return WHITE;
      default:
        return WHITE;
    }
  };

  const baseStyle: ViewStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: BORDER_RADIUS.md,
    ...getSizeStyles(),
    ...getVariantStyles(),
    ...(variant !== "ghost" && variant !== "gradient" ? SHADOW.sm : {}),
    opacity: disabled ? 0.6 : 1,
    width: fullWidth ? "100%" : undefined,
  };

  const textStyles: TextStyle = {
    ...TEXT_STYLES.button,
    color: getTextColor(),
    marginLeft: icon && iconPosition === "left" ? SPACING.sm : 0,
    marginRight: icon && iconPosition === "right" ? SPACING.sm : 0,
    ...textStyle,
  };

  const iconSize = size === "small" ? 16 : size === "large" ? 24 : 20;

  const renderContent = () => (
    <>
      {loading ? (
        <ActivityIndicator size="small" color={getTextColor()} />
      ) : (
        <>
          {icon && iconPosition === "left" && (
            <MaterialCommunityIcons
              name={icon as any}
              size={iconSize}
              color={getTextColor()}
            />
          )}
          <Text style={textStyles}>{title}</Text>
          {icon && iconPosition === "right" && (
            <MaterialCommunityIcons
              name={icon as any}
              size={iconSize}
              color={getTextColor()}
            />
          )}
        </>
      )}
    </>
  );

  if (variant === "gradient") {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
        style={[baseStyle, style]}
      >
        <GradientBackground
          variant="primary"
          style={{ flex: 1, borderRadius: BORDER_RADIUS.md }}
        >
          <View
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              paddingHorizontal: SPACING.lg,
              paddingVertical: SPACING.md,
            }}
          >
            {renderContent()}
          </View>
        </GradientBackground>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      style={[baseStyle, style]}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};
