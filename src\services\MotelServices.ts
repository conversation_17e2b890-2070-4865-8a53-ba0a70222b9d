import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, serverTimestamp, getDoc, query, where } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { Motel } from '../types';
import { getUserById } from './UserServices';

const motelsRef = collection(db, 'motels');

// Get all motels for a user (owner or manager)
export const getAllMotels = async (userId: string): Promise<Motel[]> => {
  // Query motels where user is owner
  const qOwner = query(motelsRef, where('owner_id', '==', userId));
  // Query motels where user is in managerUids array
  const qManager = query(motelsRef, where('managerUids', 'array-contains', userId));

  const [ownerSnap, managerSnap] = await Promise.all([
    getDocs(qOwner),
    getDocs(qManager)
  ]);

  // Map and merge results, avoiding duplicates
  const motelsMap = new Map<string, Motel>();
  ownerSnap.docs.forEach(docSnap => {
    motelsMap.set(docSnap.id, { ...docSnap.data(), motel_id: docSnap.id } as Motel);
  });
  managerSnap.docs.forEach(docSnap => {
    motelsMap.set(docSnap.id, { ...docSnap.data(), motel_id: docSnap.id } as Motel);
  });

  return Array.from(motelsMap.values());
};

// Get a single motel by ID
export const getMotelById = async (motelId: string): Promise<Motel | null> => {
  const motelDoc = await getDoc(doc(db, 'motels', motelId));
  if (!motelDoc.exists()) return null;
  return { ...motelDoc.data(), motel_id: motelDoc.id } as Motel;
};

// Add a new motel
export const addMotel = async (motelData: Omit<Motel, 'motel_id' | 'created_at'>): Promise<string | null> => {
  try {
    const docRef = await addDoc(motelsRef, {
      ...motelData,
      managerUids: motelData.managerUids || [],
      managerEmails: motelData.managerEmails || [],
      address: motelData.address || '',
      name: motelData.name || '',
      owner_id: motelData.owner_id || '',
      created_at: serverTimestamp(),
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding motel:', error);
    return null;
  }
};

// Update motel
export const updateMotel = async (motel_id: string, motelData: Partial<Motel>): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'motels', motel_id), {
      ...motelData,
      managerUids: motelData.managerUids || [],
      managerEmails: motelData.managerEmails || [],
      address: motelData.address || '',
      name: motelData.name || '',
      owner_id: motelData.owner_id || '',
      updated_at: serverTimestamp(),
    });
    return true;
  } catch (error) {
    console.error('Error updating motel:', error);
    return false;
  }
};

// Delete motel
export const deleteMotel = async (motel_id: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'motels', motel_id));
    return true;
  } catch (error) {
    console.error('Error deleting motel:', error);
    return false;
  }
};

// Khởi tạo một motel mặc định cho user mới
export const initialMotel = async (owner_id: string): Promise<Omit<Motel, 'motel_id' | 'created_at'>> => {
  let displayName = '';
  try {
    const user = await getUserById(owner_id);
    displayName = user?.displayName || '';
  } catch (e) {
    console.error('Không lấy được displayName:', e);
  }
  return {
    owner_id,
    name: displayName ? `Nhà trọ mới của '${displayName}'` : 'Nhà trọ mặc định',
    address: displayName ? `Địa chỉ của '${displayName}'` : 'Địa chỉ mặc định',
    managerUids: [],
    managerEmails: [],
  };
};

// Lấy motel đầu tiên của user, nếu không có thì trả về 0
export const getFirstMotel = async (owner_id: string): Promise<Motel | 0> => {
  const motels = await getAllMotels(owner_id);
  if (motels.length === 0) {
    return 0;
  }
  return motels[0];
};
