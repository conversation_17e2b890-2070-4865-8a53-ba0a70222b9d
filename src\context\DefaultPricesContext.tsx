import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface DefaultService {
  id: string;
  name: string;
  price: string;
}

interface DefaultPricesContextProps {
  defaultElectricityPrice: string;
  defaultWaterPrice: string;
  defaultServices: DefaultService[];
  setDefaultElectricityPrice: (value: string) => void;
  setDefaultWaterPrice: (value: string) => void;
  setDefaultServices: (services: DefaultService[]) => void;
  addDefaultService: (service: Omit<DefaultService, "id">) => void;
  updateDefaultService: (id: string, service: Partial<DefaultService>) => void;
  removeDefaultService: (id: string) => void;
}

const DefaultPricesContext = createContext<
  DefaultPricesContextProps | undefined
>(undefined);

const DEFAULT_SERVICES: DefaultService[] = [
  { id: "1", name: "<PERSON>á<PERSON>", price: "20000" },
  { id: "2", name: "Wifi", price: "100000" },
  { id: "3", name: "Gửi xe", price: "50000" },
];

export const DefaultPricesProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [defaultElectricityPrice, setDefaultElectricityPrice] =
    useState("4000");
  const [defaultWaterPrice, setDefaultWaterPrice] = useState("3000");
  const [defaultServices, setDefaultServices] =
    useState<DefaultService[]>(DEFAULT_SERVICES);

  // Load saved preferences on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const [savedElectricityPrice, savedWaterPrice, savedServices] =
          await Promise.all([
            AsyncStorage.getItem("defaultElectricityPrice"),
            AsyncStorage.getItem("defaultWaterPrice"),
            AsyncStorage.getItem("defaultServices"),
          ]);

        if (savedElectricityPrice)
          setDefaultElectricityPrice(savedElectricityPrice);
        if (savedWaterPrice) setDefaultWaterPrice(savedWaterPrice);
        if (savedServices) {
          const parsedServices = JSON.parse(savedServices);
          setDefaultServices(parsedServices);
        }
      } catch (error) {
        console.error("Error loading default preferences:", error);
      }
    };

    loadPreferences();
  }, []);

  // Save electricity price to AsyncStorage
  const handleSetDefaultElectricityPrice = async (value: string) => {
    try {
      await AsyncStorage.setItem("defaultElectricityPrice", value);
      setDefaultElectricityPrice(value);
    } catch (error) {
      console.error("Error saving default electricity price:", error);
      setDefaultElectricityPrice(value); // Still update state even if save fails
    }
  };

  // Save water price to AsyncStorage
  const handleSetDefaultWaterPrice = async (value: string) => {
    try {
      await AsyncStorage.setItem("defaultWaterPrice", value);
      setDefaultWaterPrice(value);
    } catch (error) {
      console.error("Error saving default water price:", error);
      setDefaultWaterPrice(value); // Still update state even if save fails
    }
  };

  // Save services to AsyncStorage
  const handleSetDefaultServices = async (services: DefaultService[]) => {
    try {
      await AsyncStorage.setItem("defaultServices", JSON.stringify(services));
      setDefaultServices(services);
    } catch (error) {
      console.error("Error saving default services:", error);
      setDefaultServices(services); // Still update state even if save fails
    }
  };

  const addDefaultService = (service: Omit<DefaultService, "id">) => {
    const newService: DefaultService = {
      ...service,
      id: Date.now().toString(),
    };
    const updatedServices = [...defaultServices, newService];
    handleSetDefaultServices(updatedServices);
  };

  const updateDefaultService = (
    id: string,
    serviceUpdate: Partial<DefaultService>
  ) => {
    const updatedServices = defaultServices.map((service) =>
      service.id === id ? { ...service, ...serviceUpdate } : service
    );
    handleSetDefaultServices(updatedServices);
  };

  const removeDefaultService = (id: string) => {
    const updatedServices = defaultServices.filter(
      (service) => service.id !== id
    );
    handleSetDefaultServices(updatedServices);
  };

  return (
    <DefaultPricesContext.Provider
      value={{
        defaultElectricityPrice,
        defaultWaterPrice,
        defaultServices,
        setDefaultElectricityPrice: handleSetDefaultElectricityPrice,
        setDefaultWaterPrice: handleSetDefaultWaterPrice,
        setDefaultServices: handleSetDefaultServices,
        addDefaultService,
        updateDefaultService,
        removeDefaultService,
      }}
    >
      {children}
    </DefaultPricesContext.Provider>
  );
};

export const useDefaultPrices = () => {
  const context = useContext(DefaultPricesContext);
  if (!context) {
    throw new Error(
      "useDefaultPrices must be used within a DefaultPricesProvider"
    );
  }
  return context;
};
