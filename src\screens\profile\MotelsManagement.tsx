import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  StatusBar,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Surface,
  IconButton,
  FAB,
  Chip,
  Divider,
  ActivityIndicator,
  Searchbar,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { getAllMotels } from "../../services/MotelServices";
import { getRoomStatsByMotel } from "../../services/roomService";
import { auth } from "../../../config/firebase";
import { useMotel } from "../../context/MotelContext";

interface ExtendedMotel {
  motel_id: string;
  owner_id: string;
  name: string;
  address: string;
  managerUids: string[];
  managerEmails: string[];
  created_at: Date;
  // UI properties
  totalRooms: number;
  availableRooms: number;
  occupiedRooms: number;
  maintenanceRooms: number;
  status: "active" | "construction" | "maintenance";
}

export default function MotelsManagement() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { t } = useLanguage();
  const { isDarkMode, colors } = useTheme();

  const [motels, setMotels] = useState<ExtendedMotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredMotels, setFilteredMotels] = useState<ExtendedMotel[]>([]);

  const { setMotelId, motelId } = useMotel();

  useEffect(() => {
    const fetchMotels = async () => {
      setLoading(true);
      try {
        const owner_id = auth.currentUser?.uid;
        if (!owner_id) {
          setMotels([]);
          setFilteredMotels([]);
          setLoading(false);
          return;
        }
        const motelsData = await getAllMotels(owner_id);
        // Lấy room stats cho từng motel
        const motelsWithStats = await Promise.all(
          motelsData.map(async (motel) => {
            try {
              const stats = await getRoomStatsByMotel(motel.motel_id);
              return {
                ...motel,
                totalRooms: stats.total,
                availableRooms: stats.available,
                occupiedRooms: stats.occupied,
                maintenanceRooms: 0, // Nếu muốn lấy số phòng sửa chữa, cần sửa getRoomStatsByMotel
                status: (motel as any).status || "active", // fallback nếu không có
                image:
                  (motel as any).image ||
                  "https://via.placeholder.com/300x150.png?text=No+Image",
                stats, // thêm stats vào object để truy cập ngoài
              };
            } catch (err) {
              console.error(
                "Lỗi khi lấy stats cho motel_id:",
                motel.motel_id,
                err
              );
              return {
                ...motel,
                totalRooms: 0,
                availableRooms: 0,
                occupiedRooms: 0,
                maintenanceRooms: 0,
                status: (motel as any).status || "active",
                image:
                  (motel as any).image ||
                  "https://via.placeholder.com/300x150.png?text=No+Image",
                stats: null,
              };
            }
          })
        );
        // Log toàn bộ danh sách stats và motel_id ra ngoài
        motelsWithStats.forEach((m) =>
          console.log("motel_id:", m.motel_id, "stats:", m.stats)
        );
        setMotels(motelsWithStats);
        setFilteredMotels(motelsWithStats);
      } catch (error) {
        setMotels([]);
        setFilteredMotels([]);
      }
      setLoading(false);
    };
    fetchMotels();
  }, []);

  // Filter motels
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredMotels(motels);
    } else {
      const filtered = motels.filter(
        (motel) =>
          motel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          motel.address.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredMotels(filtered);
    }
  }, [searchQuery, motels]);

  const handleMotelPress = (motelId: string) => {
    navigation.navigate("AddEditMotel", {
      motelId,
      mode: "edit",
    });
  };

  const handleAddMotel = () => {
    navigation.navigate("AddEditMotel", {
      mode: "create",
    });
  };

  const handleSwitchMotel = (motelId: string, motelName: string) => {
    Alert.alert(
      t("motel.switchMotel"),
      t("motel.switchMotelConfirm") + " " + motelName + " ?",
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("motel.switch"),
          onPress: () => {
            setMotelId(motelId);
          },
        },
      ]
    );
  };

  // Get status text and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "active":
        return { text: t("motel.statusActive"), color: Colors.SUCCESS };
      case "construction":
        return { text: t("motel.statusConstruction"), color: Colors.WARNING };
      case "maintenance":
        return { text: t("motel.statusMaintenance"), color: Colors.DANGER };
      default:
        return { text: t("motel.statusUnknown"), color: Colors.GRAY_DARK };
    }
  };

  const renderMotelCard = ({ item }: { item: ExtendedMotel }) => {
    const statusInfo = getStatusInfo(item.status);

    return (
      <Surface
        style={[styles.motelCard, isDarkMode && styles.darkMotelCard]}
        elevation={2}
      >
        <View style={styles.motelInfo}>
          <View style={styles.motelIconHeader}>
            <View
              style={[
                styles.motelIconContainer,
                isDarkMode && styles.darkMotelIconContainer,
              ]}
            >
              <MaterialCommunityIcons
                name="home-city"
                size={32}
                color={Colors.PRIMARY}
              />
            </View>
            <Chip
              style={[styles.statusChip, { backgroundColor: statusInfo.color }]}
              textStyle={styles.statusChipText}
            >
              {statusInfo.text}
            </Chip>
          </View>

          <View style={styles.motelHeader}>
            <Text
              style={[styles.motelName, isDarkMode && styles.darkMotelName]}
              numberOfLines={1}
            >
              {item.name}
            </Text>
          </View>

          <Text
            style={[styles.motelAddress, isDarkMode && styles.darkMotelAddress]}
            numberOfLines={2}
          >
            <MaterialCommunityIcons
              name="map-marker"
              size={14}
              color={isDarkMode ? colors.TEXT_SECONDARY : Colors.TEXT_SECONDARY}
            />{" "}
            {item.address}
          </Text>

          <Divider style={styles.divider} />

          <View style={styles.roomStats}>
            <View style={styles.roomStat}>
              <Text
                style={[
                  styles.roomStatNumber,
                  isDarkMode && styles.darkRoomStatNumber,
                ]}
              >
                {item.totalRooms}
              </Text>
              <Text
                style={[
                  styles.roomStatLabel,
                  isDarkMode && styles.darkRoomStatLabel,
                ]}
              >
                {t("motel.totalRooms")}
              </Text>
            </View>

            <View style={styles.roomStat}>
              <Text
                style={[styles.roomStatNumber, { color: Colors.AVAILABLE }]}
              >
                {item.availableRooms}
              </Text>
              <Text
                style={[
                  styles.roomStatLabel,
                  isDarkMode && styles.darkRoomStatLabel,
                ]}
              >
                {t("motel.empty")}
              </Text>
            </View>

            <View style={styles.roomStat}>
              <Text style={[styles.roomStatNumber, { color: Colors.OCCUPIED }]}>
                {item.occupiedRooms}
              </Text>
              <Text
                style={[
                  styles.roomStatLabel,
                  isDarkMode && styles.darkRoomStatLabel,
                ]}
              >
                {t("motel.rented")}
              </Text>
            </View>

            <View style={styles.roomStat}>
              <Text
                style={[styles.roomStatNumber, { color: Colors.MAINTENANCE }]}
              >
                {item.maintenanceRooms}
              </Text>
              <Text
                style={[
                  styles.roomStatLabel,
                  isDarkMode && styles.darkRoomStatLabel,
                ]}
              >
                {t("motel.maintenance")}
              </Text>
            </View>
          </View>

          {/* Action buttons */}
          <Divider style={styles.divider} />
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.updateButton]}
              onPress={() => handleMotelPress(item.motel_id)}
            >
              <MaterialCommunityIcons
                name="pencil"
                size={16}
                color={Colors.WHITE}
              />
              <Text style={styles.actionButtonText}>{t("motel.update")}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.switchButton,
                {
                  opacity: item.motel_id === motelId ? 0 : 1,
                },
              ]}
              onPress={() => handleSwitchMotel(item.motel_id, item.name)}
              disabled={item.motel_id === motelId}
            >
              <MaterialCommunityIcons
                name="swap-horizontal"
                size={16}
                color={Colors.WHITE}
              />
              <Text style={styles.actionButtonText}>{t("motel.switch")}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Surface>
    );
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />
      {/* Header */}
      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            iconColor={Colors.WHITE}
            size={24}
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
          <Text variant="headlineSmall" style={styles.headerTitle}>
            {t("motel.management")}
          </Text>
        </View>
      </Surface>
      {/* Search bar */}
      <View
        style={[
          styles.searchContainer,
          isDarkMode && styles.darkSearchContainer,
        ]}
      >
        <Searchbar
          placeholder={t("motel.searchPlaceholder")}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchBar, isDarkMode && styles.darkSearchBar]}
          iconColor={Colors.PRIMARY}
          inputStyle={isDarkMode ? styles.darkSearchInput : undefined}
          elevation={2}
        />
      </View>

      {/* Danh sách và FAB */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text
            style={[styles.loadingText, isDarkMode && styles.darkLoadingText]}
          >
            {t("motel.loading")}
          </Text>
        </View>
      ) : (
        <>
          <FlatList
            data={filteredMotels}
            keyExtractor={(item) => item.motel_id}
            renderItem={renderMotelCard}
            contentContainerStyle={styles.listContent}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            ListEmptyComponent={() => (
              <Surface
                style={[
                  styles.emptyContainer,
                  isDarkMode && styles.darkEmptyContainer,
                ]}
                elevation={0}
              >
                <MaterialCommunityIcons
                  name="home-remove"
                  size={48}
                  color={isDarkMode ? colors.GRAY_DARK : Colors.GRAY_DARK}
                />
                <Text
                  style={[styles.emptyText, isDarkMode && styles.darkEmptyText]}
                >
                  {t("motel.noMotelsFound")}
                </Text>
              </Surface>
            )}
          />
          {/* Add motel button */}
          <FAB
            icon="plus"
            style={styles.fab}
            color={Colors.WHITE}
            onPress={handleAddMotel}
          />
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 16,
    paddingBottom: 16,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_MEDIUM,
  },
  searchBar: {
    backgroundColor: Colors.WHITE,
    borderRadius: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  separator: {
    height: 16,
  },
  motelCard: {
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: Colors.WHITE,
  },
  motelInfo: {
    padding: 20,
  },
  motelIconHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  motelIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: `${Colors.PRIMARY}15`,
    justifyContent: "center",
    alignItems: "center",
  },
  motelHeader: {
    marginBottom: 8,
  },
  motelName: {
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    flex: 1,
    marginRight: 8,
  },
  statusChip: {
    height: 28,
  },
  statusChipText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.sm,
    fontWeight: Typography.FONT_WEIGHT.medium,
    marginVertical: 2,
  },
  motelAddress: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 12,
  },
  divider: {
    marginVertical: 12,
  },
  roomStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  roomStat: {
    alignItems: "center",
  },
  roomStatNumber: {
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
  },
  roomStatLabel: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
  },
  emptyContainer: {
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    marginTop: 24,
  },
  emptyText: {
    marginTop: 12,
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
  },
  fab: {
    position: "absolute",
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.PRIMARY,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  updateButton: {
    backgroundColor: Colors.PRIMARY,
  },
  switchButton: {
    backgroundColor: "#4CAF50",
  },
  actionButtonText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.sm,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },

  // Dark mode styles
  darkContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  darkSearchContainer: {
    backgroundColor: Colors.DARK.SURFACE,
    borderBottomColor: Colors.DARK.BORDER,
  },
  darkSearchBar: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  darkSearchInput: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  darkMotelCard: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  darkMotelIconContainer: {
    backgroundColor: `${Colors.PRIMARY}20`,
  },
  darkMotelName: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  darkMotelAddress: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  darkRoomStatNumber: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  darkRoomStatLabel: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  darkLoadingText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  darkEmptyContainer: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  darkEmptyText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
});
