import { auth, db } from '../../config/firebase';
import { doc, updateDoc } from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';

export interface AvatarUploadResult {
  success: boolean;
  message: string;
  imageUrl?: string;
}

// Yêu cầu quyền truy cập camera và thư viện ảnh
export const requestPermissions = async (): Promise<boolean> => {
  try {
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
    const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted';
  } catch (error) {
    console.error('Error requesting permissions:', error);
    return false;
  }
};

// Ch<PERSON>n <PERSON>nh từ thư viện
export const pickImageFromLibrary = async (): Promise<ImagePicker.ImagePickerResult | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      return null;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.7, // Giảm quality để tối ưu dung lượng
    });

    return result;
  } catch (error) {
    console.error('Error picking image from library:', error);
    return null;
  }
};

// Chụp ảnh từ camera
export const takePhotoFromCamera = async (): Promise<ImagePicker.ImagePickerResult | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      return null;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.7, // Giảm quality để tối ưu dung lượng
    });

    return result;
  } catch (error) {
    console.error('Error taking photo from camera:', error);
    return null;
  }
};

// Convert image to base64
const convertImageToBase64 = async (imageUri: string): Promise<string | null> => {
  try {
    console.log('Converting image to base64...');
    const base64 = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    
    // Tạo data URL
    const dataUrl = `data:image/jpeg;base64,${base64}`;
    console.log('Base64 conversion successful, size:', dataUrl.length);
    
    // Kiểm tra kích thước (Firestore có giới hạn 1MB per field)
    if (dataUrl.length > 1048576) { // 1MB
      console.warn('Base64 string too large:', dataUrl.length, 'bytes');
      return null;
    }
    
    return dataUrl;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    return null;
  }
};

// Cập nhật avatar của user với base64
export const updateUserAvatarBase64 = async (base64Image: string): Promise<AvatarUploadResult> => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return {
        success: false,
        message: 'Người dùng chưa đăng nhập'
      };
    }

    console.log('Updating user avatar with base64...');

    // Cập nhật profile trong Firebase Auth
    await updateProfile(user, {
      photoURL: base64Image
    });

    // Cập nhật trong Firestore
    const userDocRef = doc(db, 'users', user.uid);
    await updateDoc(userDocRef, {
      avatar: base64Image,
      updatedAt: new Date()
    });

    console.log('Avatar updated successfully');

    return {
      success: true,
      message: 'Cập nhật avatar thành công',
      imageUrl: base64Image
    };
  } catch (error) {
    console.error('Error updating user avatar:', error);
    return {
      success: false,
      message: 'Lỗi khi cập nhật avatar'
    };
  }
};

// Hàm chính để xử lý upload và update avatar
export const handleAvatarUpdate = async (imageUri: string): Promise<AvatarUploadResult> => {
  try {
    console.log('Starting avatar update process...');
    
    const user = auth.currentUser;
    if (!user) {
      console.error('No authenticated user found');
      return {
        success: false,
        message: 'Người dùng chưa đăng nhập'
      };
    }

    console.log('User authenticated:', user.uid);
    
    // Convert image to base64
    console.log('Converting image to base64...');
    const base64Image = await convertImageToBase64(imageUri);
    
    if (!base64Image) {
      return {
        success: false,
        message: 'Lỗi khi xử lý ảnh (ảnh quá lớn hoặc không hợp lệ)'
      };
    }

    console.log('Base64 conversion successful, updating avatar...');
    
    // Cập nhật avatar
    const result = await updateUserAvatarBase64(base64Image);
    return result;
  } catch (error) {
    console.error('Error handling avatar update:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
    }
    return {
      success: false,
      message: 'Có lỗi xảy ra khi cập nhật avatar'
    };
  }
};
