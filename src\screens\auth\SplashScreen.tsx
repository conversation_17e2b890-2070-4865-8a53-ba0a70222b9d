import React from "react";
import { View, StyleSheet, Image, StatusBar } from "react-native";
import { Text } from "react-native-paper";
import { Colors } from "../../theme";

const SplashScreen = () => {
  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />
      <Image
        source={require("../../../assets/images/welcome.png")}
        style={styles.logo}
        resizeMode="contain"
      />
      <Text style={styles.title}>Motel Management</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.PRIMARY,
  },
  logo: {
    width: 150,
    height: 150,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.WHITE,
  },
});

export default SplashScreen;
