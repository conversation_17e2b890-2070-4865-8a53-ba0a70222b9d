import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, StatusBar, Alert } from "react-native";
import {
  Text,
  Surface,
  IconButton,
  Button,
  Chip,
  Dialog,
  Portal,
  ActivityIndicator,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";
import { OptimizedTextInput } from "../../components/OptimizedTextInput";
import {
  getMotelById,
  addMotel,
  updateMotel,
  deleteMotel,
  getAllMotels,
} from "../../services/MotelServices";
import { auth } from "../../../config/firebase";
import { useMotel } from "../../context/MotelContext";
import { getUidByEmail } from "../../services/UserServices";

type AddEditMotelRouteProp = RouteProp<RootStackParamList, "AddEditMotel">;
type AddEditMotelNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "AddEditMotel"
>;

// Thay đổi kiểu Manager để có uid
interface Manager {
  email: string;
  uid?: string;
  name?: string;
  status: "pending" | "accepted" | "invalid";
}

export default function AddEditMotel() {
  const navigation = useNavigation<AddEditMotelNavigationProp>();
  const route = useRoute<AddEditMotelRouteProp>();
  const { colors: themeColors } = useTheme();
  const { t } = useLanguage();
  const { setMotelId, motelId: currentMotelId } = useMotel();

  const { mode, motelId } = route.params;
  const isEditMode = mode === "edit";

  const [motelName, setMotelName] = useState("");
  const [address, setAddress] = useState("");
  const [managers, setManagers] = useState<Manager[]>([]);
  const [loading, setLoading] = useState(false);
  const [motels, setMotels] = useState<{ motel_id: string; name: string }[]>(
    []
  );
  const [isOwner, setIsOwner] = useState(true);

  const [showAddManagerDialog, setShowAddManagerDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showMotelDialog, setShowMotelDialog] = useState(false);
  const [newManagerEmail, setNewManagerEmail] = useState("");
  const [emailError, setEmailError] = useState("");

  const [nameError, setNameError] = useState("");
  const [addressError, setAddressError] = useState("");
  const [selecting, setSelecting] = useState(false);

  useEffect(() => {
    if (isEditMode && motelId) {
      loadMotelData(motelId);
    }
  }, [isEditMode, motelId]);

  // Kiểm tra quyền owner
  useEffect(() => {
    if (isEditMode && motelId && auth.currentUser?.uid) {
      getMotelById(motelId).then((motel) => {
        if (motel) {
          setIsOwner(motel.owner_id === auth.currentUser?.uid);
        }
      });
    }
  }, [isEditMode, motelId]);

  useEffect(() => {
    // Lấy danh sách motel của user
    const fetchMotels = async () => {
      setSelecting(true);
      try {
        const owner_id = auth.currentUser?.uid;
        if (owner_id) {
          const motelsData = await getAllMotels(owner_id);
          setMotels(
            motelsData.map((m) => ({ motel_id: m.motel_id, name: m.name }))
          );
        }
      } catch (e) {}
      setSelecting(false);
    };
    fetchMotels();
  }, []);

  const loadMotelData = async (_id: string) => {
    setLoading(true);
    try {
      const motel = await getMotelById(_id);
      if (motel) {
        setMotelName(motel.name || "");
        setAddress(motel.address || "");
        // Map managers từ 2 trường riêng biệt
        setManagers(
          (motel.managerEmails || []).map((email: string, idx: number) => ({
            email,
            uid: motel.managerUids ? motel.managerUids[idx] : undefined,
            status: "pending",
          }))
        );
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      Alert.alert("Lỗi", "Không thể tải thông tin nhà trọ");
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAddManager = async () => {
    setEmailError("");

    if (!newManagerEmail.trim()) {
      setEmailError(t("motel.emailRequired"));
      return;
    }

    if (!validateEmail(newManagerEmail)) {
      setEmailError(t("motel.emailInvalid"));
      return;
    }

    if (managers.some((manager) => manager.email === newManagerEmail)) {
      setEmailError("Email này đã được thêm");
      return;
    }

    // Lấy UID từ email
    const uid = await getUidByEmail(newManagerEmail);
    if (!uid) {
      setEmailError("Không tìm thấy UID cho email này");
      return;
    }

    const newManager: Manager = {
      email: newManagerEmail,
      uid,
      status: "pending",
    };

    setManagers([...managers, newManager]);
    setNewManagerEmail("");
    setShowAddManagerDialog(false);
  };

  const handleRemoveManager = (email: string) => {
    Alert.alert(t("common.confirm"), t("motel.removeManager"), [
      { text: t("common.cancel"), style: "cancel" },
      {
        text: t("common.delete"),
        style: "destructive",
        onPress: () => {
          setManagers(managers.filter((manager) => manager.email !== email));
        },
      },
    ]);
  };

  const validateForm = (): boolean => {
    let isValid = true;

    if (!motelName.trim()) {
      setNameError(t("motel.nameRequired"));
      isValid = false;
    } else {
      setNameError("");
    }

    if (!address.trim()) {
      setAddressError(t("motel.addressRequired"));
      isValid = false;
    } else {
      setAddressError("");
    }

    return isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }
    setLoading(true);
    try {
      // Tách managers thành 2 mảng riêng biệt
      const managerUids = managers
        .map((m) => m.uid)
        .filter(Boolean) as string[];
      const managerEmails = managers.map((m) => m.email);
      if (isEditMode && motelId) {
        const owner_id = auth.currentUser?.uid || "";
        const success = await updateMotel(motelId, {
          name: motelName,
          address,
          managerUids,
          managerEmails,
          owner_id,
        });
        setLoading(false);
        if (success) {
          Alert.alert("Thành công", "Cập nhật nhà trọ thành công", [
            { text: "OK", onPress: () => navigation.goBack() },
          ]);
        } else {
          Alert.alert("Lỗi", "Cập nhật nhà trọ thất bại");
        }
      } else {
        // Tạo mới
        const owner_id = auth.currentUser?.uid || "";
        if (!owner_id) {
          setLoading(false);
          Alert.alert(
            "Lỗi",
            "Không xác định được người dùng. Vui lòng đăng nhập lại."
          );
          return;
        }
        const newMotelId = await addMotel({
          name: motelName,
          address,
          managerUids,
          managerEmails,
          owner_id,
        });
        setLoading(false);
        if (newMotelId) {
          Alert.alert("Thành công", "Tạo nhà trọ thành công", [
            { text: "OK", onPress: () => navigation.goBack() },
          ]);
        } else {
          Alert.alert("Lỗi", "Tạo nhà trọ thất bại");
        }
      }
    } catch (error) {
      setLoading(false);
      Alert.alert("Lỗi", "Có lỗi xảy ra, vui lòng thử lại");
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    try {
      if (motelId) {
        const success = await deleteMotel(motelId);
        setLoading(false);
        setShowDeleteDialog(false);
        if (success) {
          Alert.alert("Thành công", "Xóa nhà trọ thành công", [
            { text: "OK", onPress: () => navigation.goBack() },
          ]);
        } else {
          Alert.alert("Lỗi", "Xóa nhà trọ thất bại");
        }
      }
    } catch (error) {
      setLoading(false);
      setShowDeleteDialog(false);
      Alert.alert("Lỗi", "Có lỗi xảy ra khi xóa nhà trọ");
    }
  };

  const getManagerStatusInfo = (status: Manager["status"]) => {
    switch (status) {
      case "accepted":
        return {
          text: t("motel.statusAccepted"),
          color: Colors.SUCCESS,
          icon: "check-circle",
        };
      case "pending":
        return {
          text: t("motel.statusPending"),
          color: Colors.WARNING,
          icon: "clock-outline",
        };
      case "invalid":
        return {
          text: t("motel.statusInvalid"),
          color: Colors.DANGER,
          icon: "alert-circle",
        };
    }
  };

  return (
    <View
      style={[styles.container, { backgroundColor: themeColors.BACKGROUND }]}
    >
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />

      <Surface
        style={[styles.header, { backgroundColor: Colors.PRIMARY }]}
        elevation={2}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              iconColor={Colors.WHITE}
              size={24}
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            />
            <Text
              variant="headlineSmall"
              style={[styles.headerTitle, { color: Colors.WHITE }]}
            >
              {isEditMode ? t("motel.editMotel") : t("motel.addMotel")}
            </Text>
          </View>
          {isEditMode && (
            <IconButton
              icon="delete"
              iconColor={Colors.WHITE}
              size={24}
              onPress={() => setShowDeleteDialog(true)}
              disabled={loading || !isOwner}
              style={styles.deleteButton}
            />
          )}
        </View>
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Surface
          style={[styles.section, { backgroundColor: themeColors.SURFACE }]}
          elevation={1}
        >
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: themeColors.TEXT_PRIMARY }]}
          >
            {t("motel.basicInfo")}
          </Text>

          <OptimizedTextInput
            label={`${t("motel.motelName")} *`}
            value={motelName}
            onChangeText={setMotelName}
            mode="outlined"
            style={styles.input}
            error={!!nameError}
            errorText={nameError}
            disabled={loading || !isOwner}
          />

          <OptimizedTextInput
            label={`${t("motel.address")} *`}
            value={address}
            onChangeText={setAddress}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            error={!!addressError}
            errorText={addressError}
            disabled={loading || !isOwner}
          />
        </Surface>

        <Surface
          style={[styles.section, { backgroundColor: themeColors.SURFACE }]}
          elevation={1}
        >
          <View style={styles.sectionHeader}>
            <View>
              <Text
                variant="titleMedium"
                style={[
                  styles.sectionTitle,
                  { color: themeColors.TEXT_PRIMARY },
                ]}
              >
                {t("motel.sharedManagement")}
              </Text>
              <Text
                variant="bodySmall"
                style={[
                  styles.sectionSubtitle,
                  { color: themeColors.TEXT_SECONDARY },
                ]}
              >
                {t("motel.sharedManagementDesc")}
              </Text>
            </View>
            <IconButton
              icon="plus"
              iconColor={Colors.PRIMARY}
              size={24}
              onPress={() => setShowAddManagerDialog(true)}
              disabled={loading || !isOwner}
            />
          </View>

          {managers.length > 0 ? (
            <View style={styles.managersList}>
              {managers.map((manager) => {
                const statusInfo = getManagerStatusInfo(manager.status);
                return (
                  <View key={manager.email} style={styles.managerItem}>
                    <View style={styles.managerInfo}>
                      <MaterialCommunityIcons
                        name="account"
                        size={20}
                        color={themeColors.TEXT_SECONDARY}
                        style={styles.managerIcon}
                      />
                      <View style={styles.managerDetails}>
                        <Text
                          style={[
                            styles.managerEmail,
                            { color: themeColors.TEXT_PRIMARY },
                          ]}
                        >
                          {manager.email}
                        </Text>
                        {manager.name && (
                          <Text
                            style={[
                              styles.managerName,
                              { color: themeColors.TEXT_SECONDARY },
                            ]}
                          >
                            {manager.name}
                          </Text>
                        )}
                      </View>
                    </View>

                    <View style={styles.managerActions}>
                      <Chip
                        icon={statusInfo.icon}
                        style={[
                          styles.statusChip,
                          { backgroundColor: statusInfo.color + "20" },
                        ]}
                        textStyle={[
                          styles.statusChipText,
                          { color: statusInfo.color },
                        ]}
                        compact
                      >
                        {statusInfo.text}
                      </Chip>
                      <IconButton
                        icon="delete"
                        iconColor={Colors.DANGER}
                        size={20}
                        onPress={() => handleRemoveManager(manager.email)}
                        disabled={loading || !isOwner}
                      />
                    </View>
                  </View>
                );
              })}
            </View>
          ) : (
            <View style={styles.emptyManagers}>
              <MaterialCommunityIcons
                name="account-multiple-plus"
                size={48}
                color={themeColors.TEXT_SECONDARY}
              />
              <Text
                style={[
                  styles.emptyText,
                  { color: themeColors.TEXT_SECONDARY },
                ]}
              >
                Chưa có người quản lý nào
              </Text>
              <Text
                style={[
                  styles.emptySubtext,
                  { color: themeColors.TEXT_SECONDARY },
                ]}
              >
                Thêm email để mời người khác cùng quản lý
              </Text>
            </View>
          )}
        </Surface>

        {/* <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            marginBottom: 12,
          }}
        >
          <Button
            icon="home-city"
            mode="outlined"
            onPress={() => setShowMotelDialog(true)}
          >
            {motels.find((m) => m.motel_id === currentMotelId)?.name ||
              "Chọn nhà trọ"}
          </Button>
        </View> */}
      </ScrollView>

      <Surface
        style={[styles.footer, { backgroundColor: themeColors.SURFACE }]}
        elevation={3}
      >
        <Button
          mode="contained"
          onPress={handleSave}
          loading={loading}
          disabled={loading || !isOwner}
          style={[styles.saveButton, { backgroundColor: Colors.PRIMARY }]}
          contentStyle={styles.saveButtonContent}
        >
          {isEditMode ? t("motel.updateMotel") : t("motel.createMotel")}
        </Button>
      </Surface>

      <Portal>
        <Dialog
          visible={showAddManagerDialog}
          onDismiss={() => setShowAddManagerDialog(false)}
          style={{ backgroundColor: themeColors.SURFACE }}
        >
          <Dialog.Title style={{ color: themeColors.TEXT_PRIMARY }}>
            {t("motel.addManager")}
          </Dialog.Title>
          <Dialog.Content>
            <OptimizedTextInput
              label={t("motel.emailPlaceholder")}
              value={newManagerEmail}
              onChangeText={(text: string) => {
                setNewManagerEmail(text);
                setEmailError("");
              }}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!emailError}
              errorText={emailError}
              placeholder={t("motel.emailPlaceholder")}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowAddManagerDialog(false)}>Hủy</Button>
            <Button onPress={handleAddManager} disabled={!isOwner}>
              Thêm
            </Button>
          </Dialog.Actions>
        </Dialog>

        <Dialog
          visible={showDeleteDialog}
          onDismiss={() => setShowDeleteDialog(false)}
          style={{ backgroundColor: themeColors.SURFACE }}
        >
          <Dialog.Icon icon="alert" />
          <Dialog.Title
            style={{ color: themeColors.TEXT_PRIMARY, textAlign: "center" }}
          >
            {t("motel.confirmDelete")}
          </Dialog.Title>
          <Dialog.Content>
            <Text
              style={{ color: themeColors.TEXT_PRIMARY, textAlign: "center" }}
            >
              {t("motel.deleteConfirmMessage")}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              onPress={handleDelete}
              textColor={Colors.DANGER}
              loading={loading}
              disabled={loading || !isOwner}
            >
              {t("common.delete")}
            </Button>
          </Dialog.Actions>
        </Dialog>

        {/* Dialog chọn nhà trọ */}
        {/*
        <Dialog
          visible={showMotelDialog}
          onDismiss={() => setShowMotelDialog(false)}
        >
          <Dialog.Title>Chọn nhà trọ</Dialog.Title>
          <Dialog.Content>
            {selecting ? (
              <ActivityIndicator />
            ) : motels.length === 0 ? (
              <Text>Không có nhà trọ nào</Text>
            ) : (
              motels.map((motel) => (
                <Button
                  key={motel.motel_id}
                  mode={
                    currentMotelId === motel.motel_id ? "contained" : "outlined"
                  }
                  style={{ marginVertical: 4 }}
                  onPress={() => {
                    setMotelId(motel.motel_id);
                    setShowMotelDialog(false);
                  }}
                >
                  {motel.name}
                </Button>
              ))
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowMotelDialog(false)}>Đóng</Button>
          </Dialog.Actions>
        </Dialog>
        */}
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 8,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  deleteButton: {
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: Typography.FONT_WEIGHT.bold,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 8,
    marginLeft: 4,
  },
  managersList: {
    gap: 12,
  },
  managerItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.GRAY_LIGHT + "40",
  },
  managerInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  managerIcon: {
    marginRight: 12,
  },
  managerDetails: {
    flex: 1,
  },
  managerEmail: {
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  managerName: {
    fontSize: Typography.FONT_SIZE.sm,
    marginTop: 2,
  },
  managerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  statusChip: {
    height: 28,
  },
  statusChipText: {
    fontSize: Typography.FONT_SIZE.xs,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  emptyManagers: {
    alignItems: "center",
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.medium,
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: Typography.FONT_SIZE.sm,
    textAlign: "center",
    marginTop: 4,
  },
  footer: {
    padding: 16,
    paddingBottom: 32,
  },
  saveButton: {
    borderRadius: 8,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
});
