import React, { useState } from "react";
import { View, StyleSheet, TouchableOpacity, Image, Alert } from "react-native";
import { Text, TextInput, ActivityIndicator } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors } from "../../theme";
import { useLanguage } from "../../context/LanguageContext";
import { auth } from "../../../config/firebase";
import { sendPasswordResetEmail } from "firebase/auth";

const ForgotPasswordScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { t } = useLanguage();

  const [email, setEmail] = useState("");
  const [resetSent, setResetSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async () => {
    if (!email) {
      Alert.alert(t("common.error"), t("auth.emailRequired"));
      return;
    }

    // Kiểm tra định dạng email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert(t("common.error"), t("auth.invalidEmail"));
      return;
    }

    setIsLoading(true);

    try {
      await sendPasswordResetEmail(auth, email);
      setIsLoading(false);
      setResetSent(true);
    } catch (error: any) {
      setIsLoading(false);
      let errorMessage = t("auth.resetPasswordError");

      if (error.code === "auth/user-not-found") {
        errorMessage = t("auth.userNotFound");
      } else if (error.code === "auth/invalid-email") {
        errorMessage = t("auth.invalidEmail");
      }

      Alert.alert(t("common.error"), errorMessage);
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/login.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </SafeAreaView>

      <View style={styles.formContainer}>
        {resetSent ? (
          <View style={styles.successContainer}>
            <MaterialCommunityIcons
              name="check-circle"
              size={60}
              color={Colors.SUCCESS}
              style={styles.successIcon}
            />
            <Text style={styles.successTitle}>{t("auth.emailSent")}</Text>
            <Text style={styles.successText}>
              {t("auth.resetInstructions")}
            </Text>
            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={() => navigation.navigate("Login")}
            >
              <Text style={styles.backToLoginText}>
                {t("auth.backToLogin")}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{t("auth.email")}</Text>
              <TextInput
                style={styles.input}
                placeholder={t("auth.emailPlaceholder")}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
                textColor={Colors.TEXT_PRIMARY}
              />
            </View>

            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleResetPassword}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.WHITE} size="small" />
              ) : (
                <Text style={styles.resetButtonText}>
                  {t("auth.resetPassword")}
                </Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.backToLoginLink}
              onPress={() => navigation.navigate("Login")}
            >
              <Text style={styles.backToLoginLinkText}>
                {t("auth.backToLogin")}
              </Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
  },
  header: {
    paddingTop: 20,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 200,
    height: 150,
  },
  formContainer: {
    flex: 1,
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 15,
    marginTop: 10,
  },
  description: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 20,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFEBEE",
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: Colors.DANGER,
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 5,
    marginLeft: 5,
  },
  input: {
    backgroundColor: Colors.WHITE,
  },
  resetButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
  },
  resetButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
  backToLoginLink: {
    alignItems: "center",
  },
  backToLoginLinkText: {
    color: Colors.PRIMARY,
    fontSize: 16,
  },
  successContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 10,
  },
  successText: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
    marginBottom: 30,
  },
  backToLoginButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    width: "100%",
  },
  backToLoginText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ForgotPasswordScreen;
