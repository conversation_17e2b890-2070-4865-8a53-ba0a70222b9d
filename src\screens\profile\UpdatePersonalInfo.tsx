import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import { Text, Surface, Button, IconButton } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { auth } from "../../../config/firebase";
import { getUserById, updateUser } from "../../services/UserServices";
import { useLanguage } from "../../context/LanguageContext";
import { OptimizedTextInput } from "../../components/OptimizedTextInput";
import * as ImagePicker from "expo-image-picker";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

export default function UpdatePersonalInfo() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { t } = useLanguage();

  const [userData, setUserData] = useState({
    name: "An",
    email: "<EMAIL>",
    phone: "0912345678",
    role: "Quản lý",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  });

  const [name, setName] = useState(userData.name);
  const [email, setEmail] = useState(userData.email);
  const [phone, setPhone] = useState(userData.phone);
  const [avatar, setAvatar] = useState(userData.avatar);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone: "",
  });

  useEffect(() => {
    const fetchUser = async () => {
      if (auth.currentUser?.uid) {
        const userData = await getUserById(auth.currentUser.uid);
        if (userData) {
          setUserData({
            name: userData.displayName || "Default",
            email: userData.email || "Default",
            phone: userData.phone || "Default",
            role: "Quản lý", // hoặc lấy từ userData nếu có
            avatar:
              userData.avatar ||
              "https://randomuser.me/api/portraits/men/32.jpg",
          });
          setName(userData.displayName || "");
          setEmail(userData.email || "");
          setPhone(userData.phone || "");
          setAvatar(
            userData.avatar || "https://randomuser.me/api/portraits/men/32.jpg"
          );
        }
      }
    };
    fetchUser();
  }, []);

  const validateForm = () => {
    let isValid = true;
    const newErrors = { name: "", email: "", phone: "" };

    if (!name.trim()) {
      newErrors.name = "Tên không được để trống";
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = "Email không được để trống";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email không hợp lệ";
      isValid = false;
    }

    if (!phone.trim()) {
      newErrors.phone = "Số điện thoại không được để trống";
      isValid = false;
    } else if (!/^[0-9]{10}$/.test(phone)) {
      newErrors.phone = "Số điện thoại phải có 10 chữ số";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSelectImage = async () => {
    // Yêu cầu quyền truy cập thư viện ảnh
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Quyền bị từ chối",
        "Bạn cần cấp quyền truy cập thư viện ảnh để chọn ảnh đại diện."
      );
      return;
    }
    // Mở thư viện ảnh
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.7,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setAvatar(result.assets[0].uri);
    }
  };

  // Hàm upload ảnh lên Firebase Storage
  const uploadAvatarToStorage = async (
    uri: string,
    userId: string
  ): Promise<string | null> => {
    if (!uri || !uri.startsWith("file://") || !userId) {
      console.error("Avatar uri hoặc userId không hợp lệ");
      return null;
    }
    try {
      const response = await fetch(uri);
      if (!response.ok) {
        console.error("Không thể fetch file ảnh từ uri", uri);
        return null;
      }
      const blob = await response.blob();
      const storage = getStorage();
      const fileName = `avatar_${Date.now()}.jpg`;
      const storageRef = ref(storage, `avatars/${userId}/${fileName}`);
      await uploadBytes(storageRef, blob);
      const downloadURL = await getDownloadURL(storageRef);
      if (!downloadURL) {
        console.error("Không lấy được downloadURL sau khi upload");
        return null;
      }
      return downloadURL;
    } catch (error) {
      // Chỉ log khi thực sự upload thất bại
      console.error("Lỗi upload avatar thực sự:", error, JSON.stringify(error));
      // console.error("Server response:", error.serverResponse);
      return null;
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    setIsLoading(true);
    try {
      let avatarUrl = avatar;
      // Nếu avatar là uri cục bộ (file mới chọn), thì upload lên Storage
      if (avatar && avatar.startsWith("file://") && auth.currentUser?.uid) {
        const uploadedUrl = await uploadAvatarToStorage(
          avatar,
          auth.currentUser.uid
        );
        if (uploadedUrl) {
          avatarUrl = uploadedUrl;
        } else {
          Alert.alert("Lỗi", "Upload ảnh đại diện thất bại. Vui lòng thử lại.");
          setIsLoading(false);
          return;
        }
      }
      if (auth.currentUser?.uid) {
        const success = await updateUser(auth.currentUser.uid, {
          displayName: name,
          email: email,
          phone: phone,
          avatar: avatarUrl,
        });
        if (success) {
          Alert.alert("Thành công", "Cập nhật thông tin thành công!");
        } else {
          Alert.alert("Lỗi", "Cập nhật thông tin thất bại!");
        }
      }
    } catch (error) {
      Alert.alert("Lỗi", "Có lỗi xảy ra khi cập nhật thông tin!");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />

      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            iconColor={Colors.WHITE}
            size={24}
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
          <Text variant="headlineSmall" style={styles.headerTitle}>
            Cập nhật thông tin
          </Text>
        </View>
      </Surface>

      <ScrollView style={styles.content}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: avatar }} style={styles.avatar} />
          <TouchableOpacity
            style={styles.editAvatarButton}
            onPress={handleSelectImage}
          >
            <MaterialCommunityIcons
              name="camera"
              size={20}
              color={Colors.WHITE}
            />
          </TouchableOpacity>
        </View>

        {/* Form */}
        <Surface style={styles.formCard} elevation={1}>
          <OptimizedTextInput
            label="Họ và tên"
            value={name}
            onChangeText={setName}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
            errorText={errors.name}
            leftIcon="account"
          />

          <OptimizedTextInput
            label="Email"
            value={email}
            mode="outlined"
            style={styles.input}
            error={!!errors.email}
            errorText={errors.email}
            keyboardType="email-address"
            leftIcon="email"
            disabled={true}
          />

          <OptimizedTextInput
            label={t("validation.phoneNumber")}
            value={phone}
            onChangeText={setPhone}
            mode="outlined"
            style={styles.input}
            error={!!errors.phone}
            errorText={errors.phone}
            keyboardType="phone-pad"
            leftIcon="phone"
          />
        </Surface>

        {/* Action Buttons - Outside of form */}
        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.submitButton}
            icon="content-save"
            loading={isLoading}
            disabled={isLoading}
          >
            {t("validation.saveChanges")}
          </Button>

          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.cancelButton}
            icon="close-circle"
            disabled={isLoading}
          >
            Hủy
          </Button>
        </View>

        <View style={styles.noteContainer}>
          <Text style={styles.noteText}>
            * Lưu ý: Thông tin cá nhân của bạn sẽ được bảo mật theo chính sách
            của chúng tôi.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  avatarContainer: {
    alignItems: "center",
    marginVertical: 24,
    position: "relative",
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: Colors.WHITE,
  },
  editAvatarButton: {
    position: "absolute",
    bottom: 0,
    right: "35%",
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.WHITE,
  },
  formCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    marginHorizontal: 4,
  },
  input: {
    backgroundColor: Colors.WHITE,
  },
  errorText: {
    color: Colors.DANGER,
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 8,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 16,
    gap: 12,
    paddingHorizontal: 16,
  },
  submitButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
  },
  cancelButton: {
    borderColor: Colors.PRIMARY,
  },
  noteContainer: {
    marginVertical: 16,
    paddingHorizontal: 8,
  },
  noteText: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    fontStyle: "italic",
  },
});
