import { collection, getDocs, query, where, onSnapshot, addDoc, serverTimestamp, doc, getDoc, updateDoc, DocumentReference, deleteDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { auth } from '../../config/firebase';
import { Room, Resident, HoaDon, ChiSoDienNuoc } from '../types';

// Collection references
const roomsRef = collection(db, 'rooms');
const residentsRef = collection(db, 'residents');
const hoaDonRef = collection(db, 'hoa_don');
const chiSoDienNuocRef = collection(db, 'chi_so_dien_nuoc');

// Add new room
export const addRoom = async (roomData: Omit<Room, 'room_id'>) => {
  try {
    if (!auth.currentUser) {
      throw new Error('User must be authenticated to create a room');
    }
    
    const docRef = await addDoc(roomsRef, {
      ...roomData,
      ownerId: auth.currentUser.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Error adding room:', error);
    return { success: false, error };
  }
};

// Thêm hàm addBulkRooms vào roomService
export const addBulkRooms = async (
  rooms: Omit<Room, 'room_id'>[]
): Promise<{
  success: boolean;
  ids?: string[];
  duplicated?: Omit<Room, 'room_id'>[];
  error?: any;
}> => {
  try {
    console.log('[addBulkRooms] called with rooms:', rooms);
    if (!auth.currentUser) {
      throw new Error('User must be authenticated to create rooms');
    }
    if (!rooms.length) {
      console.log('[addBulkRooms] No rooms to add.');
      return { success: true, ids: [], duplicated: [] };
    }

    // Giả định tất cả phòng cùng motel_id (nếu không, lấy motel_id từ từng phòng)
    const motelId = rooms[0].motel_id;
    console.log('[addBulkRooms] motelId:', motelId);
    if (!motelId) throw new Error('Missing motel_id');

    // Lấy tất cả room_number đã tồn tại trong motel này
    const q = query(roomsRef, where('motel_id', '==', motelId));
    const snapshot = await getDocs(q);
    const existingRoomNumbers = new Set(
      snapshot.docs.map(doc => doc.data().room_number)
    );
    console.log('[addBulkRooms] existingRoomNumbers:', Array.from(existingRoomNumbers));

    // Lọc các phòng unique (room_number chưa tồn tại)
    const uniqueRooms = rooms.filter(
      r => !existingRoomNumbers.has(r.room_number)
    );
    const duplicated = rooms.filter(
      r => existingRoomNumbers.has(r.room_number)
    );
    console.log('[addBulkRooms] uniqueRooms:', uniqueRooms);
    console.log('[addBulkRooms] duplicated:', duplicated);

    const ids: string[] = [];
    for (const roomData of uniqueRooms) {
      const docRef = await addDoc(roomsRef, {
        ...roomData,
        ownerId: auth.currentUser.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      ids.push(docRef.id);
      console.log('[addBulkRooms] Added room:', roomData.room_number, 'with id:', docRef.id);
    }
    console.log('[addBulkRooms] All added ids:', ids);
    return { success: true, ids, duplicated };
  } catch (error) {
    console.error('[addBulkRooms] Error adding bulk rooms:', error);
    return { success: false, error };
  }
};

// Get all rooms for current user
export const getAllRooms = async (): Promise<Room[]> => {
  try {
    if (!auth.currentUser) {
      throw new Error('User must be authenticated to fetch rooms');
    }
    
//     console.log('Fetching rooms for current user...');
    const q = query(roomsRef, where('ownerId', '==', auth.currentUser.uid));
    const snapshot = await getDocs(q);
    const rooms = snapshot.docs.map(doc => ({ ...doc.data(), room_id: doc.id } as Room));
//     console.log('Fetched rooms:', rooms);
    return rooms;
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return [];
  }
};



// Get a single room by ID
export const getRoomById = async (roomId: string): Promise<Room | null> => {
  try {
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));
    if (!roomDoc.exists()) {
      return null;
    }
    return { ...roomDoc.data(), room_id: roomDoc.id } as Room;
  } catch (error) {
    console.error('Error fetching room:', error);
    return null;
  }
};

// Update room details
export const updateRoom = async (roomId: string, roomData: Partial<Room>): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'rooms', roomId), {
      ...roomData,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error updating room:', error);
    return false;
  }
};

// Delete a room
export const deleteRoom = async (roomId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'rooms', roomId));
    return true;
  } catch (error) {
    console.error('Error deleting room:', error);
    return false;
  }
};


// Subscribe to rooms updates for current user
export const subscribeToRooms = (callback: (rooms: Room[]) => void, motelId: string) => {
  if (!motelId) {
    console.error('motelId must be provided to subscribe to rooms');
    callback([]);
    return () => {};
  }
  // Chỉ cần motelId, fetch toàn bộ room thuộc motel này
  const unsub = onSnapshot(
    query(roomsRef, where('motel_id', '==', motelId)),
    (snapshot) => {
      const rooms: Room[] = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          room_id: doc.id,
          created_at: data.created_at?.toDate() || new Date(),
          start_date: data.start_date?.toDate() || null,
          end_date: data.end_date?.toDate() || null,
        } as Room;
      });
      callback(rooms);
    }
  );
  return () => {
    unsub();
  };
};

// Remove resident
export const removeResident = async (residentId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'residents', residentId));
    return true;
  } catch (error) {
    console.error('Error removing resident:', error);
    return false;
  }
};

// Lấy thống kê phòng theo motelId và ownerId
// Hàm này sẽ được sử dụng trong reportScreen
export const getRoomStatsByMotel = async (motelId: string): Promise<{ total: number; available: number; occupied: number; maintenance: number }> => {
  try {
    if (!motelId) {
      throw new Error('Missing motelId');
    }
    // Lấy tất cả room thuộc motelId
    const q = query(roomsRef, where('motel_id', '==', motelId));
    const snapshot = await getDocs(q);
    const rooms = snapshot.docs.map(doc => ({ ...doc.data(), room_id: doc.id } as Room));
    const total = rooms.length;
    const available = rooms.filter(r => r.status === 'available').length;
    const occupied = rooms.filter(r => r.status === 'occupied').length;
    const maintenance = rooms.filter(r => r.status === 'maintenance').length;
    return { total, available, occupied, maintenance };
  } catch (error) {
    console.error('Error fetching room stats for motel:', motelId, error);
    return { total: 0, available: 0, occupied: 0, maintenance: 0 };
  }
};

// Lấy tất cả phòng theo motelId
export const getRoomsByMotelId = async (motelId: string): Promise<Room[]> => {
  try {
    if (!motelId) throw new Error('Missing motelId');
    const q = query(collection(db, 'rooms'), where('motel_id', '==', motelId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ ...doc.data(), room_id: doc.id } as Room));
  } catch (error) {
    console.error('Error fetching rooms by motelId:', error);
    return [];
  }
};

// Đặt phòng về trạng thái bảo trì hoặc ngưng bảo trì
export const setRoomMaintenance = async (roomId: string, maintenance: boolean = true): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'rooms', roomId), {
      status: maintenance ? 'maintenance' : 'available',
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error setting room maintenance status:', error);
    return false;
  }
};

