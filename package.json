{"name": "cozynest", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-masked-view/masked-view": "0.3.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.6.1", "expo": "~50.0.0", "expo-auth-session": "~5.4.0", "expo-crypto": "~12.8.1", "expo-document-picker": "~11.10.1", "expo-file-system": "~16.0.9", "expo-linear-gradient": "~12.7.2", "expo-media-library": "~15.9.2", "expo-sharing": "~11.10.0", "expo-status-bar": "~1.11.1", "expo-web-browser": "~12.8.2", "firebase": "^10.13.0", "i18n-js": "^4.5.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.14.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "^14.1.0", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "3.8.0", "react-native-webview": "13.6.4", "react-redux": "^9.2.0", "xlsx": "^0.18.5", "expo-image-picker": "~14.7.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/i18n-js": "^4.0.1", "@types/react": "~18.2.45", "typescript": "^5.3.3"}, "private": true}