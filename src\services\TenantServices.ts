import { collection, getDocs, query, where, addDoc, serverTimestamp, doc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { auth } from '../../config/firebase';
import { Room, Resident } from '../types';
import { addLog } from "./logService";

// Collection references
const tenantsRef = collection(db, 'tenants');
const roomsRef = collection(db, 'rooms');

// Interface cho tenant data
interface TenantData {
  name: string;
  phone: string;
  identity_number: string;
  room_id: string;
  is_main_tenant: boolean;
  created_at?: any;
}

// Thêm tenant mới và cập nhật phòng
export const addTenant = async (tenantData: TenantData): Promise<{success: boolean; id?: string; error?: any}> => {
  try {
    if (!auth.currentUser) {
      throw new Error('Người dùng phải đăng nhập để thêm tenant');
    }
    
    // Verify the user owns the room before creating the tenant
    const roomRef = doc(roomsRef, tenantData.room_id);
    const roomDoc = await getDoc(roomRef);
    
    if (!roomDoc.exists()) {
      throw new Error('Không tìm thấy phòng');
    }
    

    // Now add the tenant with all required fields according to security rules
    const docRef = await addDoc(tenantsRef, {
      ...tenantData,
      created_at: serverTimestamp()
    });
    // Ghi log thao tác thêm tenant
    await addLog({
      action: "add",
      targetId: docRef.id,
      targetType: "tenant",
      motelId: roomDoc.data().motel_id || "",
      after: tenantData,
      description: `Thêm người thuê: ${tenantData.name}`,
    });
    return { success: true, id: docRef.id };
  } 
  catch (error) {
    console.error('Lỗi khi thêm tenant:', error);
    return { success: false, error };
  } 
  finally {
    // console.log('Hoàn tất xử lý addTenant');
  }
};



// Lấy thông tin tenant theo ID
export const getTenantById = async (tenantId: string): Promise<Resident | null> => {
  try {
    const tenantDoc = await getDoc(doc(tenantsRef, tenantId));
    if (!tenantDoc.exists()) {
      return null;
    }
    return { ...tenantDoc.data(), resident_id: tenantDoc.id } as Resident;
  } catch (error) {
    console.error('Lỗi khi lấy thông tin tenant:', error);
    return null;
  }
};

// Lấy danh sách tenant theo phòng
export const getTenantsByRoomId = async (roomId: string): Promise<Resident[]> => {
  try {
    const q = query(tenantsRef, where('room_id', '==', roomId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      ...doc.data(),
      resident_id: doc.id,
      created_at: doc.data().created_at?.toDate() || new Date()
    }) as Resident);
  } catch (error) {
    console.error('Lỗi khi lấy danh sách tenant:', error);
    return [];
  }
};

// Lấy danh sách tenant theo motelId
export const getTenantsByMotelId = async (motelId: string): Promise<Resident[]> => {
  try {
    const roomsQ = query(roomsRef, where('motel_id', '==', motelId));
    const roomsSnap = await getDocs(roomsQ);
    const roomIds = roomsSnap.docs.map(doc => doc.id);
    if (roomIds.length === 0) return [];
    // Firestore 'in' chỉ hỗ trợ tối đa 10 phần tử, cần chia nhỏ nếu nhiều hơn
    const tenants: Resident[] = [];
    for (let i = 0; i < roomIds.length; i += 10) {
      const batch = roomIds.slice(i, i + 10);
      const tenantsQ = query(tenantsRef, where('room_id', 'in', batch));
      const tenantsSnap = await getDocs(tenantsQ);
      tenants.push(...tenantsSnap.docs.map(doc => ({
        ...doc.data(),
        resident_id: doc.id,
        created_at: doc.data().created_at?.toDate() || new Date()
      }) as Resident));
    }
    return tenants;
  } catch (error) {
    console.error('Lỗi khi lấy tenants theo motelId:', error);
    return [];
  }
};

// Cập nhật thông tin tenant
export const updateTenant = async (
  tenantId: string,
  tenantData: Partial<TenantData>
): Promise<boolean> => {
  try {
    const tenantRef = doc(tenantsRef, tenantId);
    const tenantDoc = await getDoc(tenantRef);

    if (!tenantDoc.exists()) {
      throw new Error('Không tìm thấy tenant');
    }

    const before = tenantDoc.data();
    await updateDoc(tenantRef, {
      ...tenantData,
      updatedAt: serverTimestamp()
    });

    // Nếu thay đổi trạng thái main tenant, cập nhật phòng
    if (tenantData.is_main_tenant !== undefined) {
      const roomRef = doc(roomsRef, tenantDoc.data().room_id);
      await updateDoc(roomRef, {
        tenant_id: tenantData.is_main_tenant ? tenantId : null,
        status: tenantData.is_main_tenant ? 'occupied' : 'available',
        updatedAt: serverTimestamp()
      });
    }

    // Lấy motel_id từ room
    let motelId = "";
    if (before && before.room_id) {
      const roomRef = doc(roomsRef, before.room_id);
      const roomDoc = await getDoc(roomRef);
      if (roomDoc.exists()) {
        motelId = roomDoc.data().motel_id || "";
      }
    }

    // Ghi log thao tác cập nhật tenant
    await addLog({
      action: "edit",
      targetId: tenantId,
      targetType: "tenant",
      motelId,
      before,
      after: tenantData,
      description: `Cập nhật người thuê: ${tenantData.name || before.name}`,
    });

    return true;
  } catch (error) {
    console.error('Lỗi khi cập nhật tenant:', error);
    return false;
  }
};

// Xóa tenant
export const deleteTenant = async (tenantId: string): Promise<boolean> => {
  try {
    const tenantRef = doc(tenantsRef, tenantId);
    const tenantDoc = await getDoc(tenantRef);

    if (!tenantDoc.exists()) {
      throw new Error('Không tìm thấy tenant');
    }

    // Nếu là main tenant, cập nhật trạng thái phòng
    if (tenantDoc.data().is_main_tenant) {
      await updateDoc(doc(roomsRef, tenantDoc.data().room_id), {
        tenant_id: null,
        status: 'available',
        updatedAt: serverTimestamp()
      });
    }

    // Lấy motel_id từ room để ghi log
    let motelId = "";
    if (tenantDoc.data().room_id) {
      const roomRef = doc(roomsRef, tenantDoc.data().room_id);
      const roomDoc = await getDoc(roomRef);
      if (roomDoc.exists()) {
        motelId = roomDoc.data().motel_id || "";
      }
    }

    // Ghi log thao tác xóa tenant
    await addLog({
      action: "delete",
      targetId: tenantId,
      targetType: "tenant",
      motelId,
      before: tenantDoc.data(),
      description: `Xóa người thuê: ${tenantDoc.data().name}`,
    });

    await deleteDoc(tenantRef);
    return true;
  } catch (error) {
    console.error('Lỗi khi xóa tenant:', error);
    return false;
  }
};

// Chuyển đổi main tenant
export const changeMainTenant = async (
  roomId: string,
  oldMainTenantId: string,
  newMainTenantId: string
): Promise<boolean> => {
  try {
    // Cập nhật old main tenant
    await updateDoc(doc(tenantsRef, oldMainTenantId), {
      is_main_tenant: false,
      updatedAt: serverTimestamp()
    });

    // Cập nhật new main tenant
    await updateDoc(doc(tenantsRef, newMainTenantId), {
      is_main_tenant: true,
      updatedAt: serverTimestamp()
    });

    // Cập nhật room
    await updateDoc(doc(roomsRef, roomId), {
      tenant_id: newMainTenantId,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Lỗi khi chuyển đổi main tenant:', error);
    return false;
  }
};
