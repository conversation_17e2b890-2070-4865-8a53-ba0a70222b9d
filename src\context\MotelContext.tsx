import React, { createContext, useContext, useState } from "react";

type MotelContextType = {
  motelId: string | null;
  setMotelId: (id: string) => void;
};

const MotelContext = createContext<MotelContextType>({
  motelId: null,
  setMotelId: () => {},
});

export const useMotel = () => useContext(MotelContext);

export const MotelProvider = ({ children }: { children: React.ReactNode }) => {
  const [motelId, setMotelId] = useState<string | null>(null);
  return (
    <MotelContext.Provider value={{ motelId, setMotelId }}>
      {children}
    </MotelContext.Provider>
  );
};
