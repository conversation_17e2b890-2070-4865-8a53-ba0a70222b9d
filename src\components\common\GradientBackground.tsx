import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { ViewStyle } from 'react-native';
import { 
  GRADIENT_PRIMARY, 
  GRADIENT_SECONDARY, 
  GRADIENT_SUCCESS, 
  GRADIENT_WARNING, 
  GRADIENT_DANGER,
  GRADIENT_INFO,
  GRADIENT_BACKGROUND_LIGHT,
  GRADIENT_BACKGROUND_DARK
} from '../../theme/colors';

interface GradientBackgroundProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'background-light' | 'background-dark';
  style?: ViewStyle;
  start?: { x: number; y: number };
  end?: { x: number; y: number };
}

const gradientMap = {
  primary: GRADIENT_PRIMARY,
  secondary: GRADIENT_SECONDARY,
  success: GRADIENT_SUCCESS,
  warning: GRADIENT_WARNING,
  danger: GRADIENT_DANGER,
  info: GRADIENT_INFO,
  'background-light': GRADIENT_BACKGROUND_LIGHT,
  'background-dark': GRADIENT_BACKGROUND_DARK,
};

export const GradientBackground: React.FC<GradientBackgroundProps> = ({
  children,
  variant = 'primary',
  style,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
}) => {
  const colors = gradientMap[variant];

  return (
    <LinearGradient
      colors={colors}
      start={start}
      end={end}
      style={style}
    >
      {children}
    </LinearGradient>
  );
};
