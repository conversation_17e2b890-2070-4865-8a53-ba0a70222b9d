import React, { useState, useEffect } from "react";
import {
  Dialog,
  Portal,
  Text,
  Button,
  Divider,
  HelperText,
  Chip,
  RadioButton,
} from "react-native-paper";
import { View, StyleSheet, ScrollView } from "react-native";
import { useTheme } from "../context/ThemeContext";
import { useLanguage } from "../context/LanguageContext";
import { OptimizedTextInput } from "./OptimizedTextInput";
import { HoaDon } from "../types";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface CollectPaymentDialogProps {
  visible: boolean;
  onDismiss: () => void;
  onCollectPayment: (paymentData: PaymentData) => void;
  bill: HoaDon | null;
}

interface PaymentData {
  amount: number;
  paymentMethod: "cash" | "bank_transfer" | "momo" | "other";
  note: string;
  collectedDate: Date;
}

const PAYMENT_METHODS = [
  { id: "cash", label: "payment.cash", icon: "cash" },
  { id: "bank_transfer", label: "payment.bankTransfer", icon: "bank" },
  { id: "momo", label: "payment.momo", icon: "cellphone" },
  { id: "other", label: "payment.other", icon: "credit-card" },
];

export default function CollectPaymentDialog({
  visible,
  onDismiss,
  onCollectPayment,
  bill,
}: CollectPaymentDialogProps) {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();

  const [paymentData, setPaymentData] = useState<PaymentData>({
    amount: 0,
    paymentMethod: "cash",
    note: "",
    collectedDate: new Date(),
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (visible && bill) {
      setPaymentData({
        amount: bill.tong_tien,
        paymentMethod: "cash",
        note: "",
        collectedDate: new Date(),
      });
      setErrors({});
    }
  }, [visible, bill]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!paymentData.amount || paymentData.amount <= 0) {
      newErrors.amount = t("validation.required");
    }

    if (bill && paymentData.amount > bill.tong_tien) {
      newErrors.amount = t("payment.amountExceedsBill");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCollectPayment = () => {
    if (!validateForm()) return;

    onCollectPayment(paymentData);
    onDismiss();
  };

  const handleCancel = () => {
    setPaymentData({
      amount: 0,
      paymentMethod: "cash",
      note: "",
      collectedDate: new Date(),
    });
    setErrors({});
    onDismiss();
  };

  if (!bill) return null;

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={handleCancel}
        style={[
          styles.dialog,
          {
            backgroundColor: isDarkMode ? colors.CARD_BACKGROUND : colors.WHITE,
          },
        ]}
      >
        <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
          {t("payment.collectPayment")}
        </Dialog.Title>

        <Divider style={{ backgroundColor: colors.DIVIDER }} />

        <Dialog.Content style={styles.content}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.billSummary}>
              <Text style={[styles.billTitle, { color: colors.TEXT_PRIMARY }]}>
                {t("payment.billFor")} {bill.thang_hoa_don}
              </Text>
              <Text style={[styles.billAmount, { color: "#70C4D7" }]}>
                {formatCurrency(bill.tong_tien)}
              </Text>
              <Text
                style={[styles.billPeriod, { color: colors.TEXT_SECONDARY }]}
              >
                {new Date(bill.created_at).toLocaleDateString("vi-VN")}
              </Text>
            </View>

            {/* Payment Amount */}
            <OptimizedTextInput
              mode="outlined"
              label={t("payment.paymentAmount")}
              value={paymentData.amount.toString()}
              onChangeText={(text) =>
                setPaymentData({ ...paymentData, amount: Number(text) || 0 })
              }
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              keyboardType="numeric"
              error={!!errors.amount}
              errorText={errors.amount}
              leftIcon="cash"
              rightIcon="currency-usd"
            />

            {/* Quick Amount Buttons */}
            <View style={styles.quickAmountContainer}>
              <Text
                style={[styles.sectionLabel, { color: colors.TEXT_PRIMARY }]}
              >
                {t("payment.quickAmount")}
              </Text>
              <View style={styles.quickAmountButtons}>
                <Chip
                  mode="outlined"
                  onPress={() =>
                    setPaymentData({ ...paymentData, amount: bill.tong_tien })
                  }
                  style={styles.quickAmountChip}
                >
                  {t("payment.fullAmount")}
                </Chip>
                <Chip
                  mode="outlined"
                  onPress={() =>
                    setPaymentData({
                      ...paymentData,
                      amount: bill.tong_tien / 2,
                    })
                  }
                  style={styles.quickAmountChip}
                >
                  {t("payment.halfAmount")}
                </Chip>
              </View>
            </View>

            {/* Payment Method */}
            <View style={styles.paymentMethodContainer}>
              <Text
                style={[styles.sectionLabel, { color: colors.TEXT_PRIMARY }]}
              >
                {t("payment.paymentMethod")}
              </Text>
              <RadioButton.Group
                onValueChange={(value) =>
                  setPaymentData({
                    ...paymentData,
                    paymentMethod: value as any,
                  })
                }
                value={paymentData.paymentMethod}
              >
                {PAYMENT_METHODS.map((method) => (
                  <View key={method.id} style={styles.paymentMethodItem}>
                    <View style={styles.paymentMethodInfo}>
                      <MaterialCommunityIcons
                        name={method.icon as any}
                        size={24}
                        color={colors.TEXT_PRIMARY}
                      />
                      <Text
                        style={[
                          styles.paymentMethodLabel,
                          { color: colors.TEXT_PRIMARY },
                        ]}
                      >
                        {t(method.label)}
                      </Text>
                    </View>
                    <RadioButton value={method.id} color="#70C4D7" />
                  </View>
                ))}
              </RadioButton.Group>
            </View>

            {/* Payment Note */}
            <OptimizedTextInput
              mode="outlined"
              label={t("payment.paymentNote")}
              value={paymentData.note}
              onChangeText={(text) =>
                setPaymentData({ ...paymentData, note: text })
              }
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              multiline
              numberOfLines={3}
              leftIcon="note-text"
              placeholder={t("payment.paymentNotePlaceholder")}
            />
          </ScrollView>
        </Dialog.Content>

        <Dialog.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={handleCancel}
            textColor={colors.TEXT_SECONDARY}
          >
            {t("common.cancel")}
          </Button>
          <Button
            mode="contained"
            onPress={handleCollectPayment}
            buttonColor="#70C4D7"
            textColor={colors.WHITE}
            icon="cash-check"
          >
            {t("payment.collectPayment")}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}

const styles = StyleSheet.create({
  dialog: {
    marginHorizontal: 20,
    borderRadius: 12,
    maxHeight: "90%",
  },
  content: {
    paddingVertical: 20,
    maxHeight: 500,
  },
  billSummary: {
    backgroundColor: "#70C4D7",
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    alignItems: "center",
  },
  billTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
    marginBottom: 4,
  },
  billAmount: {
    fontSize: 24,
    fontWeight: "bold",
    color: "white",
    marginBottom: 4,
  },
  billPeriod: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
  },
  input: {
    marginBottom: 8,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  quickAmountContainer: {
    marginVertical: 16,
  },
  quickAmountButtons: {
    flexDirection: "row",
    gap: 8,
  },
  quickAmountChip: {
    flex: 1,
  },
  paymentMethodContainer: {
    marginVertical: 16,
  },
  paymentMethodItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  paymentMethodInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  paymentMethodLabel: {
    fontSize: 16,
  },
  actions: {
    paddingHorizontal: 24,
    paddingBottom: 20,
    gap: 8,
  },
});
