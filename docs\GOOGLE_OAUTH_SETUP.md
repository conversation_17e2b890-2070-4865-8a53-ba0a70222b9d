# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the Motel Management React Native app.

## Current Implementation Status

✅ **Development Mode (Expo Go)**: Google OAuth is implemented with a development placeholder that simulates the authentication flow.

🔧 **Production Mode**: Requires proper Google OAuth credentials and a development build to work with real Google authentication.

## For Development Testing (Current Setup)

The current implementation works in Expo Go with a development placeholder:

1. **Run the app**: `npm start`
2. **Navigate to Login/SignUp screen**
3. **Tap "Sign in with Google" button**
4. **You'll see a development dialog** explaining the setup
5. **Choose "Simulate Success"** to test the authentication flow
6. **The app will simulate a successful Google sign-in**

This allows you to test the UI and authentication flow without setting up real Google OAuth credentials.

## For Production Setup

## Prerequisites

1. Firebase project already set up (which you have)
2. React Native development environment
3. Google Cloud Console access

## Step 1: Enable Google Sign-In in Firebase

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `quanlynhatro-8afcf`
3. Navigate to **Authentication** > **Sign-in method**
4. Click on **Google** provider
5. Enable Google sign-in
6. Set your project support email
7. Save the configuration

## Step 2: Get Web Client ID

1. In Firebase Console, go to **Project Settings** (gear icon)
2. Scroll down to **Your apps** section
3. Find your web app configuration
4. Copy the **Web client ID** (it looks like: `123456789-abcdefghijklmnop.apps.googleusercontent.com`)
5. Replace the placeholder in `src/utils/googleAuth.ts`:

```typescript
const WEB_CLIENT_ID = "YOUR_ACTUAL_WEB_CLIENT_ID_HERE";
```

## Step 3: Configure Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (should be the same as Firebase)
3. Navigate to **APIs & Services** > **Credentials**
4. You should see OAuth 2.0 Client IDs created by Firebase

### For Android:

1. Click **Create Credentials** > **OAuth client ID**
2. Select **Android** as application type
3. Enter package name: `com.motelmanagement.client`
4. Get your SHA-1 certificate fingerprint:

   ```bash
   # For debug keystore
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

   # For release keystore (when building for production)
   keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
   ```

5. Enter the SHA-1 fingerprint
6. Create the client ID

### For iOS:

1. Click **Create Credentials** > **OAuth client ID**
2. Select **iOS** as application type
3. Enter bundle ID: `com.motelmanagement.client`
4. Create the client ID

## Step 4: Download Configuration Files

### For Android:

1. In Firebase Console, go to **Project Settings**
2. Download `google-services.json`
3. Place it in the root directory of your project

### For iOS:

1. In Firebase Console, go to **Project Settings**
2. Download `GoogleService-Info.plist`
3. Place it in the root directory of your project

## Step 5: Update app.json

The app.json has already been configured with the correct paths:

```json
{
  "expo": {
    "ios": {
      "bundleIdentifier": "com.motelmanagement.client",
      "googleServicesFile": "./GoogleService-Info.plist"
    },
    "android": {
      "package": "com.motelmanagement.client",
      "googleServicesFile": "./google-services.json"
    },
    "plugins": ["@react-native-google-signin/google-signin"]
  }
}
```

## Step 6: Test the Implementation

1. Run the app: `npm start`
2. Navigate to the Login screen
3. Tap the "Sign in with Google" button
4. Complete the Google sign-in flow
5. Verify that the user is authenticated in Firebase Console

## Troubleshooting

### Common Issues:

1. **"Developer Error" or "Sign in failed"**

   - Check that the Web Client ID is correct
   - Ensure SHA-1 fingerprint is added for Android
   - Verify bundle ID matches for iOS

2. **"Google Play Services not available"**

   - This error occurs on Android emulators without Google Play Services
   - Test on a real device or use an emulator with Google Play Services

3. **"Network Error"**

   - Check internet connection
   - Verify Firebase project configuration

4. **"Invalid client ID"**
   - Double-check the Web Client ID in `googleAuth.ts`
   - Ensure the client ID is from the correct Firebase project

### Debug Steps:

1. Check Firebase Authentication logs
2. Verify Google Cloud Console credentials
3. Test on both Android and iOS devices
4. Check network connectivity

## Security Notes

1. Never commit your actual client IDs to version control if they're sensitive
2. Use environment variables for production builds
3. Regularly rotate credentials if needed
4. Monitor authentication logs for suspicious activity

## Additional Resources

- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Google Sign-In for React Native](https://github.com/react-native-google-signin/google-signin)
- [Expo Google Sign-In Guide](https://docs.expo.dev/guides/authentication/#google)
