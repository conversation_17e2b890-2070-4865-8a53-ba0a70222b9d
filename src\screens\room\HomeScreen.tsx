import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  ImageBackground,
  ScrollView,
} from "react-native";
import {
  IconButton,
  Searchbar,
  Text,
  Surface,
  FAB,
  Portal,
  Dialog,
  RadioButton,
  Button,
  Chip,
  Checkbox,
  SegmentedButtons,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { RoomCard } from "../../components";
import { useNavigation } from "@react-navigation/native";
import { RootStackParamList, Room, Resident } from "../../types";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Colors, Spacing, Typography } from "../../theme";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { subscribeToRooms } from "../../services/roomService";
import { getTenantsByRoomId } from "../../services/TenantServices";
import { useMotel } from "../../context/MotelContext";
import { getMotelById } from "../../services/MotelServices";

const HomeScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { t } = useLanguage();
  const { isDarkMode, colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [rooms, setRooms] = useState<Room[]>([]);
  // Remove filteredRooms state - will use useMemo instead
  const [loading, setLoading] = useState(true);
  const [roomResidents, setRoomResidents] = useState<{
    [key: string]: Resident[];
  }>({});

  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [roomTypeFilters, setRoomTypeFilters] = useState<string[]>([]);
  const [priceRangeFilter, setPriceRangeFilter] = useState<[number, number]>([
    0, 5000000,
  ]);
  const [hasAmenityFilter, setHasAmenityFilter] = useState<string[]>([]);

  const [filterDialogVisible, setFilterDialogVisible] = useState(false);
  const [tempStatusFilter, setTempStatusFilter] = useState<string | null>(null);
  const [tempRoomTypeFilters, setTempRoomTypeFilters] = useState<string[]>([]);
  const [tempPriceRangeFilter, setTempPriceRangeFilter] = useState<
    [number, number]
  >([0, 5000000]);
  const [tempHasAmenityFilter, setTempHasAmenityFilter] = useState<string[]>(
    []
  );

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [motelName, setMotelName] = useState<string>("");

  const allRoomTypes = Array.from(
    new Set(rooms.map((room) => room.loaiPhong).flat())
  );

  const allAmenities = Array.from(
    new Set(rooms.map((room) => room.tienNghi).flat())
  );
  const { motelId } = useMotel();
  useEffect(() => {
    if (!motelId) return;
    console.log("Setting up rooms subscription...");
    const unsubscribe = subscribeToRooms((fetchedRooms) => {
      // Sort rooms by room_number (as number if possible, otherwise as string)
      const sortedRooms = [...fetchedRooms].sort((a, b) => {
        const aNum = parseInt(a.room_number, 10);
        const bNum = parseInt(b.room_number, 10);
        if (!isNaN(aNum) && !isNaN(bNum)) {
          return aNum - bNum;
        }
        return a.room_number.localeCompare(b.room_number, undefined, {
          numeric: true,
          sensitivity: "base",
        });
      });
      console.log("Received rooms from server (sorted):", sortedRooms);
      setRooms(sortedRooms);
      setLoading(false);
    }, motelId);

    return () => unsubscribe();
  }, [motelId]);

  useEffect(() => {
    const fetchResidents = async () => {
      const residentsData: { [key: string]: Resident[] } = {};
      for (const room of rooms) {
        const residents = await getTenantsByRoomId(room.room_id);
        residentsData[room.room_id] = residents;
      }
      setRoomResidents(residentsData);
    };

    if (rooms.length > 0) {
      fetchResidents();
    }
  }, [rooms]);

  useEffect(() => {
    if (!motelId) return;
    getMotelById(motelId).then((motel) => {
      if (motel && motel.name) setMotelName(motel.name);
    });
  }, [motelId]);

  // Optimized room press handler
  const handleRoomPress = useCallback(
    (room: Room) => {
      navigation.navigate("RoomDetails", {
        roomId: room.room_id,
        roomNumber: room.room_number,
        motelId: room.motel_id,
        initialStatus: room.status,
        price: room.gia,
        deposit: room.tienCoc,
        area: room.dienTich,
        amenities: room.tienNghi,
        roomTypes: room.loaiPhong,
        note: room.ghiChu,
      });
    },
    [navigation]
  );

  useEffect(() => {
    let count = 0;
    if (statusFilter) count++;
    if (roomTypeFilters.length > 0) count++;
    if (priceRangeFilter[0] > 0 || priceRangeFilter[1] < 5000000) count++;
    if (hasAmenityFilter.length > 0) count++;
    setActiveFiltersCount(count);
  }, [statusFilter, roomTypeFilters, priceRangeFilter, hasAmenityFilter]);

  // Memoized filtered rooms for better performance
  const filteredRooms = useMemo(() => {
    let filtered = rooms;

    if (searchQuery) {
      filtered = filtered.filter((room) =>
        room.room_number.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter((room) => room.status === statusFilter);
    }

    if (roomTypeFilters.length > 0) {
      filtered = filtered.filter((room) =>
        roomTypeFilters.some((type) => room.loaiPhong.includes(type))
      );
    }

    if (priceRangeFilter[0] > 0 || priceRangeFilter[1] < 5000000) {
      filtered = filtered.filter(
        (room) =>
          room.gia >= priceRangeFilter[0] && room.gia <= priceRangeFilter[1]
      );
    }
    if (hasAmenityFilter.length > 0) {
      filtered = filtered.filter((room) =>
        hasAmenityFilter.every((amenity) => room.tienNghi.includes(amenity))
      );
    }

    console.log("Filtered rooms:", filtered);
    return filtered;
  }, [
    rooms,
    searchQuery,
    statusFilter,
    roomTypeFilters,
    priceRangeFilter,
    hasAmenityFilter,
  ]);

  // Optimized refresh function
  const handleRefresh = useCallback(() => {
    setLoading(true);
    if (!motelId) return;
    subscribeToRooms((fetchedRooms) => {
      setRooms(fetchedRooms);
      setLoading(false);
    }, motelId);
  }, [motelId]);

  // Optimized render item function
  const renderRoomItem = useCallback(
    ({ item }: { item: Room }) => (
      <RoomCard
        room={item}
        residents={roomResidents[item.room_id] || []}
        onPress={() => handleRoomPress(item)}
      />
    ),
    [roomResidents, handleRoomPress]
  );

  const openFilterDialog = () => {
    setTempStatusFilter(statusFilter);
    setTempRoomTypeFilters([...roomTypeFilters]);
    setTempPriceRangeFilter([...priceRangeFilter]);
    setTempHasAmenityFilter([...hasAmenityFilter]);
    setFilterDialogVisible(true);
  };

  const applyFilters = () => {
    setStatusFilter(tempStatusFilter);
    setRoomTypeFilters([...tempRoomTypeFilters]);
    setPriceRangeFilter([...tempPriceRangeFilter]);
    setHasAmenityFilter([...tempHasAmenityFilter]);
    setFilterDialogVisible(false);
  };

  const resetFilters = () => {
    setTempStatusFilter(null);
    setTempRoomTypeFilters([]);
    setTempPriceRangeFilter([0, 5000000]);
    setTempHasAmenityFilter([]);
  };

  const resetAllFilters = () => {
    setStatusFilter(null);
    setRoomTypeFilters([]);
    setPriceRangeFilter([0, 5000000]);
    setHasAmenityFilter([]);
  };

  const cancelFilters = () => {
    setFilterDialogVisible(false);
  };

  const toggleRoomTypeFilter = (type: string) => {
    if (tempRoomTypeFilters.includes(type)) {
      setTempRoomTypeFilters(tempRoomTypeFilters.filter((t) => t !== type));
    } else {
      setTempRoomTypeFilters([...tempRoomTypeFilters, type]);
    }
  };

  const toggleAmenityFilter = (amenity: string) => {
    if (tempHasAmenityFilter.includes(amenity)) {
      setTempHasAmenityFilter(
        tempHasAmenityFilter.filter((a) => a !== amenity)
      );
    } else {
      setTempHasAmenityFilter([...tempHasAmenityFilter, amenity]);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " đ";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "occupied":
        return Colors.OCCUPIED;
      case "available":
        return Colors.AVAILABLE;
      case "maintenance":
        return Colors.MAINTENANCE;
      default:
        return Colors.GRAY_DARK;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "occupied":
        return t("room.statusOccupied");
      case "available":
        return t("room.statusAvailable");
      case "maintenance":
        return t("room.statusMaintenance");
      default:
        return t("room.statusAll");
    }
  };

  const renderFilterDialog = () => (
    <Portal>
      <Dialog
        visible={filterDialogVisible}
        onDismiss={cancelFilters}
        style={[styles.dialog, isDarkMode && styles.darkDialog]}
      >
        <Dialog.ScrollArea style={styles.dialogScrollArea}>
          <ScrollView>
            {/* Status filter */}
            <View style={styles.filterTitleContainer}>
              <MaterialCommunityIcons
                name="home-city"
                size={24}
                color={Colors.PRIMARY}
              />
              <Text
                style={[
                  styles.filterDialogTitle,
                  isDarkMode && styles.darkFilterDialogTitle,
                ]}
              >
                1. {t("room.filterStatus")}
              </Text>
            </View>
            <RadioButton.Group
              onValueChange={(value) =>
                setTempStatusFilter(value === "all" ? null : value)
              }
              value={tempStatusFilter || "all"}
            >
              <View
                style={[styles.radioItem, isDarkMode && styles.darkRadioItem]}
              >
                <RadioButton value="all" color={Colors.PRIMARY} />
                <Text
                  style={[
                    styles.radioLabel,
                    isDarkMode && styles.darkRadioLabel,
                  ]}
                >
                  {t("room.statusAll")}
                </Text>
              </View>
              <View
                style={[styles.radioItem, isDarkMode && styles.darkRadioItem]}
              >
                <RadioButton value="occupied" color={Colors.PRIMARY} />
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: Colors.OCCUPIED },
                  ]}
                />
                <Text
                  style={[
                    styles.radioLabel,
                    isDarkMode && styles.darkRadioLabel,
                  ]}
                >
                  {t("room.statusOccupied")}
                </Text>
              </View>
              <View
                style={[styles.radioItem, isDarkMode && styles.darkRadioItem]}
              >
                <RadioButton value="available" color={Colors.PRIMARY} />
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: Colors.AVAILABLE },
                  ]}
                />
                <Text
                  style={[
                    styles.radioLabel,
                    isDarkMode && styles.darkRadioLabel,
                  ]}
                >
                  {t("room.statusAvailable")}
                </Text>
              </View>
              <View
                style={[styles.radioItem, isDarkMode && styles.darkRadioItem]}
              >
                <RadioButton value="maintenance" color={Colors.PRIMARY} />
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: Colors.MAINTENANCE },
                  ]}
                />
                <Text
                  style={[
                    styles.radioLabel,
                    isDarkMode && styles.darkRadioLabel,
                  ]}
                >
                  {t("room.statusMaintenance")}
                </Text>
              </View>
            </RadioButton.Group>

            {/* Room type filter */}
            <View style={styles.filterTitleContainer}>
              <MaterialCommunityIcons
                name="format-list-checks"
                size={24}
                color={Colors.PRIMARY}
              />
              <Text
                style={[
                  styles.filterDialogTitle,
                  isDarkMode && styles.darkFilterDialogTitle,
                ]}
              >
                2. {t("room.filterType")}
              </Text>
            </View>
            <View style={styles.checkboxContainer}>
              {allRoomTypes.map((type) => (
                <View
                  key={type}
                  style={[
                    styles.checkboxItem,
                    isDarkMode && styles.darkCheckboxItem,
                  ]}
                >
                  <Checkbox
                    status={
                      tempRoomTypeFilters.includes(type)
                        ? "checked"
                        : "unchecked"
                    }
                    onPress={() => toggleRoomTypeFilter(type)}
                    color={Colors.PRIMARY}
                  />
                  <Text
                    style={[
                      styles.checkboxLabel,
                      isDarkMode && styles.darkCheckboxLabel,
                    ]}
                  >
                    {type}
                  </Text>
                </View>
              ))}
            </View>

            {/* Price range filter */}
            <View style={styles.filterTitleContainer}>
              <MaterialCommunityIcons
                name="cash-multiple"
                size={24}
                color={Colors.PRIMARY}
              />
              <Text
                style={[
                  styles.filterDialogTitle,
                  isDarkMode && styles.darkFilterDialogTitle,
                ]}
              >
                3. {t("room.filterPrice")}
              </Text>
            </View>
            <View
              style={[
                styles.priceRangeContainer,
                isDarkMode && styles.darkPriceRangeContainer,
              ]}
            >
              <Text style={styles.priceRangeLabel}>
                {formatCurrency(tempPriceRangeFilter[0])} -{" "}
                {formatCurrency(tempPriceRangeFilter[1])}
              </Text>
              <View style={styles.priceInputContainer}>
                <View
                  style={[
                    styles.priceInput,
                    isDarkMode && styles.darkPriceInput,
                  ]}
                >
                  <Text
                    style={[
                      styles.priceInputLabel,
                      isDarkMode && styles.darkPriceInputLabel,
                    ]}
                  >
                    {t("room.filterMinPrice")}
                  </Text>
                  <SegmentedButtons
                    value={tempPriceRangeFilter[0].toString()}
                    onValueChange={(value) => {
                      const min = parseInt(value);
                      setTempPriceRangeFilter([min, tempPriceRangeFilter[1]]);
                    }}
                    buttons={[
                      { value: "0", label: "0" },
                      { value: "1000000", label: "1M" },
                      { value: "2000000", label: "2M" },
                      { value: "3000000", label: "3M" },
                    ]}
                    style={styles.segmentedButton}
                  />
                </View>
                <View
                  style={[
                    styles.priceInput,
                    isDarkMode && styles.darkPriceInput,
                  ]}
                >
                  <Text
                    style={[
                      styles.priceInputLabel,
                      isDarkMode && styles.darkPriceInputLabel,
                    ]}
                  >
                    {t("room.filterMaxPrice")}
                  </Text>
                  <SegmentedButtons
                    value={tempPriceRangeFilter[1].toString()}
                    onValueChange={(value) => {
                      const max = parseInt(value);
                      setTempPriceRangeFilter([tempPriceRangeFilter[0], max]);
                    }}
                    buttons={[
                      { value: "2000000", label: "2M" },
                      { value: "3000000", label: "3M" },
                      { value: "4000000", label: "4M" },
                      { value: "5000000", label: "5M" },
                    ]}
                    style={styles.segmentedButton}
                  />
                </View>
              </View>
            </View>

            {/* Amenities filter */}
            <View style={styles.filterTitleContainer}>
              <MaterialCommunityIcons
                name="star-box-multiple"
                size={24}
                color={Colors.PRIMARY}
              />
              <Text
                style={[
                  styles.filterDialogTitle,
                  isDarkMode && styles.darkFilterDialogTitle,
                ]}
              >
                4. {t("room.filterAmenities")}
              </Text>
            </View>
            <View style={styles.checkboxContainer}>
              {allAmenities.map((amenity) => (
                <View
                  key={amenity}
                  style={[
                    styles.checkboxItem,
                    isDarkMode && styles.darkCheckboxItem,
                  ]}
                >
                  <Checkbox
                    status={
                      tempHasAmenityFilter.includes(amenity)
                        ? "checked"
                        : "unchecked"
                    }
                    onPress={() => toggleAmenityFilter(amenity)}
                    color={Colors.PRIMARY}
                  />
                  <Text
                    style={[
                      styles.checkboxLabel,
                      isDarkMode && styles.darkCheckboxLabel,
                    ]}
                  >
                    {amenity}
                  </Text>
                </View>
              ))}
            </View>
          </ScrollView>
        </Dialog.ScrollArea>
        <Dialog.Actions style={styles.dialogActions}>
          <Button
            onPress={resetFilters}
            textColor={
              isDarkMode ? Colors.DARK.TEXT_SECONDARY : Colors.TEXT_SECONDARY
            }
            style={styles.dialogActionButton}
          >
            {t("common.reset")}
          </Button>
          <Button
            onPress={cancelFilters}
            textColor={
              isDarkMode ? Colors.DARK.TEXT_SECONDARY : Colors.TEXT_SECONDARY
            }
            style={styles.dialogActionButton}
          >
            {t("common.cancel")}
          </Button>
          <Button
            onPress={applyFilters}
            textColor={Colors.WHITE}
            buttonColor={Colors.PRIMARY}
            style={styles.dialogActionButton}
            labelStyle={styles.applyButtonLabel}
          >
            {t("common.apply")}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {renderFilterDialog()}

      <ImageBackground
        source={{
          uri: "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d4/The_Lauren_condo_Bethesda_MD_2021-12-12_10-11-55_1.jpg/1200px-The_Lauren_condo_Bethesda_MD_2021-12-12_10-11-55_1.jpg",
        }}
        style={styles.headerBackground}
      >
        <View
          style={[styles.headerOverlay, isDarkMode && styles.darkHeaderOverlay]}
        />
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{motelName || "Default Motel"}</Text>
          <Text style={styles.headerSubtitle}>
            {filteredRooms.length} {t("room.rooms")}
          </Text>
        </View>
      </ImageBackground>

      {/* Search bar with filter button */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBarContainer}>
          <Searchbar
            placeholder={t("room.searchPlaceholder")}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={[styles.searchBar, isDarkMode && styles.darkSearchBar]}
            iconColor={Colors.PRIMARY}
            inputStyle={[
              styles.searchInput,
              isDarkMode && styles.darkSearchInput,
            ]}
            elevation={2}
          />
          <IconButton
            icon="filter-variant"
            size={24}
            style={styles.filterButton}
            iconColor={
              activeFiltersCount > 0
                ? Colors.WHITE
                : isDarkMode
                ? Colors.DARK.TEXT_PRIMARY
                : Colors.TEXT_PRIMARY
            }
            containerColor={
              activeFiltersCount > 0
                ? Colors.PRIMARY
                : isDarkMode
                ? Colors.DARK.GRAY_LIGHT
                : Colors.GRAY_LIGHT
            }
            onPress={openFilterDialog}
          />
        </View>

        {/* Active filters display */}
        {(statusFilter ||
          roomTypeFilters.length > 0 ||
          hasAmenityFilter.length > 0 ||
          priceRangeFilter[0] > 0 ||
          priceRangeFilter[1] < 5000000) && (
          <View style={styles.activeFiltersContainer}>
            <Text
              style={[
                styles.activeFiltersLabel,
                isDarkMode && styles.darkActiveFiltersLabel,
              ]}
            >
              {t("room.activeFilters")}:
            </Text>

            {/* Status filter chip */}
            {statusFilter && (
              <Chip
                mode="flat"
                onClose={() => setStatusFilter(null)}
                style={[
                  styles.activeFilterChip,
                  { backgroundColor: getStatusColor(statusFilter) + "20" },
                ]}
                textStyle={{ color: getStatusColor(statusFilter) }}
              >
                {getStatusText(statusFilter) || statusFilter}
              </Chip>
            )}

            {/* Room type filter chips */}
            {roomTypeFilters.map((type) => (
              <Chip
                key={type}
                mode="flat"
                onClose={() =>
                  setRoomTypeFilters(roomTypeFilters.filter((t) => t !== type))
                }
                style={[
                  styles.activeFilterChip,
                  { backgroundColor: Colors.PRIMARY + "20" },
                ]}
                textStyle={{ color: Colors.PRIMARY }}
              >
                {t(type) || type || ""}
              </Chip>
            ))}

            {/* Price range filter chip */}
            {(priceRangeFilter[0] > 0 || priceRangeFilter[1] < 5000000) && (
              <Chip
                mode="flat"
                onClose={() => setPriceRangeFilter([0, 5000000])}
                style={[
                  styles.activeFilterChip,
                  { backgroundColor: Colors.SECONDARY + "20" },
                ]}
                textStyle={{ color: Colors.SECONDARY }}
              >
                {formatCurrency(priceRangeFilter[0])} -{" "}
                {formatCurrency(priceRangeFilter[1])}
              </Chip>
            )}

            {/* Amenity filter chips */}
            {hasAmenityFilter.map((amenity) => (
              <Chip
                key={amenity}
                mode="flat"
                onClose={() =>
                  setHasAmenityFilter(
                    hasAmenityFilter.filter((a) => a !== amenity)
                  )
                }
                style={[
                  styles.activeFilterChip,
                  { backgroundColor: Colors.ACCENT + "20" },
                ]}
                textStyle={{ color: Colors.ACCENT }}
              >
                {t(amenity) || amenity || ""}
              </Chip>
            ))}

            {/* Clear all filters button */}
            {activeFiltersCount > 1 && (
              <Chip
                mode="outlined"
                onPress={resetAllFilters}
                style={styles.clearAllChip}
                textStyle={{ color: Colors.DANGER }}
              >
                {t("room.clearAllFilters")}
              </Chip>
            )}
          </View>
        )}
      </View>

      {/* Room list */}
      <FlatList
        data={filteredRooms}
        keyExtractor={(item) => item.room_id}
        contentContainerStyle={styles.listContent}
        refreshing={loading}
        onRefresh={handleRefresh}
        renderItem={renderRoomItem}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        ListEmptyComponent={() => (
          <Surface
            style={[
              styles.emptyContainer,
              isDarkMode && styles.darkEmptyContainer,
            ]}
            elevation={0}
          >
            <Text
              style={[styles.emptyText, isDarkMode && styles.darkEmptyText]}
            >
              {t("room.noRoomsFound")}
            </Text>
          </Surface>
        )}
      />

      {/* Add room button */}
      <FAB
        icon="plus"
        style={styles.fab}
        color={Colors.WHITE}
        onPress={() => navigation.navigate("AddRoom", {})}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  darkContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  headerBackground: {
    height: 200,
    width: "100%",
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(99, 102, 241, 0.8)", // Modern indigo overlay
  },
  darkHeaderOverlay: {
    backgroundColor: "rgba(79, 70, 229, 0.9)", // Darker indigo for dark mode
  },
  headerContent: {
    position: "absolute",
    top: 40,
    left: 0,
    right: 0,
    padding: Spacing.SPACING.lg,
    paddingTop: Spacing.SPACING.xl,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.xxxl + 4,
    fontWeight: Typography.FONT_WEIGHT.bold,
    marginBottom: Spacing.SPACING.xs,
    textAlign: "center",
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: "rgba(255, 255, 255, 0.95)",
    fontSize: Typography.FONT_SIZE.lg,
    textAlign: "center",
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  searchContainer: {
    paddingHorizontal: Spacing.SPACING.lg,
    marginTop: -Spacing.SPACING.xl,
    marginBottom: Spacing.SPACING.lg,
    gap: Spacing.SPACING.md,
  },
  searchBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.SPACING.md,
    ...Spacing.SHADOW.md,
  },
  searchBar: {
    flex: 1,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    backgroundColor: Colors.WHITE,
    elevation: 4,
  },
  darkSearchBar: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
    elevation: 6,
  },
  searchInput: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
  },
  darkSearchInput: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  filterButton: {
    margin: 0,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
  },
  activeFiltersContainer: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    gap: Spacing.SPACING.xs,
  },
  activeFiltersLabel: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    marginRight: Spacing.SPACING.xs,
  },
  darkActiveFiltersLabel: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  activeFilterChip: {
    height: 32,
    marginBottom: Spacing.SPACING.xs,
  },
  clearAllChip: {
    height: 32,
    marginBottom: Spacing.SPACING.xs,
    borderColor: Colors.DANGER,
  },
  dialog: {
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    marginHorizontal: Spacing.SPACING.md,
    overflow: "hidden",
    ...Spacing.SHADOW.lg,
  },
  darkDialog: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  dialogActionButton: {
    marginHorizontal: Spacing.SPACING.xs,
  },
  applyButtonLabel: {
    fontWeight: Typography.FONT_WEIGHT.bold,
    fontSize: Typography.FONT_SIZE.md,
  },
  dialogScrollArea: {
    paddingHorizontal: Spacing.SPACING.md,
    maxHeight: 500,
  },
  filterTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.SPACING.md,
    marginTop: Spacing.SPACING.md,
  },
  filterDialogTitle: {
    fontSize: Typography.FONT_SIZE.lg,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.PRIMARY,
    marginLeft: Spacing.SPACING.sm,
    flex: 1,
    paddingBottom: Spacing.SPACING.xs,
    borderBottomWidth: 2,
    borderBottomColor: Colors.PRIMARY + "30",
  },
  darkFilterDialogTitle: {
    color: Colors.PRIMARY,
    borderBottomColor: Colors.PRIMARY + "50",
  },
  radioItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.SPACING.sm,
    paddingVertical: Spacing.SPACING.xs,
    paddingHorizontal: Spacing.SPACING.sm,
    borderRadius: Spacing.BORDER_RADIUS.md,
    backgroundColor: Colors.GRAY_LIGHT + "50",
  },
  darkRadioItem: {
    backgroundColor: Colors.DARK.GRAY_MEDIUM + "50",
  },
  radioLabel: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
    marginLeft: Spacing.SPACING.xs,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  darkRadioLabel: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  statusDot: {
    width: 14,
    height: 14,
    borderRadius: 7,
    marginRight: Spacing.SPACING.xs,
    borderWidth: 1,
    borderColor: Colors.WHITE,
  },
  checkboxContainer: {
    marginBottom: Spacing.SPACING.md,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.SPACING.xs,
  },
  checkboxItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.SPACING.xs,
    paddingVertical: Spacing.SPACING.xs,
    paddingHorizontal: Spacing.SPACING.sm,
    borderRadius: Spacing.BORDER_RADIUS.md,
    backgroundColor: Colors.GRAY_LIGHT + "50",
    minWidth: "45%",
  },
  darkCheckboxItem: {
    backgroundColor: Colors.DARK.GRAY_MEDIUM + "50",
  },
  checkboxLabel: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
    marginLeft: Spacing.SPACING.xs,
    flex: 1,
  },
  darkCheckboxLabel: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  priceRangeContainer: {
    backgroundColor: Colors.GRAY_LIGHT + "50",
    borderRadius: Spacing.BORDER_RADIUS.md,
    padding: Spacing.SPACING.md,
  },
  darkPriceRangeContainer: {
    backgroundColor: Colors.DARK.GRAY_MEDIUM + "50",
  },
  priceRangeLabel: {
    fontSize: Typography.FONT_SIZE.lg,
    color: Colors.SECONDARY,
    fontWeight: Typography.FONT_WEIGHT.bold,
    marginBottom: Spacing.SPACING.md,
    textAlign: "center",
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.BORDER_RADIUS.md,
    paddingVertical: Spacing.SPACING.sm,
    paddingHorizontal: Spacing.SPACING.md,
    ...Spacing.SHADOW.sm,
  },
  darkPriceRangeLabel: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
    color: Colors.SECONDARY,
  },
  priceInputContainer: {
    gap: Spacing.SPACING.md,
  },
  priceInput: {
    marginBottom: Spacing.SPACING.md,
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.BORDER_RADIUS.md,
    padding: Spacing.SPACING.sm,
    ...Spacing.SHADOW.sm,
  },
  darkPriceInput: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  priceInputLabel: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
    marginBottom: Spacing.SPACING.sm,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  darkPriceInputLabel: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  segmentedButton: {
    marginTop: Spacing.SPACING.xs,
  },
  divider: {
    marginVertical: Spacing.SPACING.lg,
    height: 1,
    backgroundColor: Colors.PRIMARY + "20",
  },
  listContent: {
    padding: Spacing.SPACING.md,
    paddingBottom: Spacing.SPACING.xxl,
  },
  separator: {
    height: Spacing.SPACING.md,
  },
  emptyContainer: {
    padding: Spacing.SPACING.xl,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  darkEmptyContainer: {
    backgroundColor: "transparent",
  },
  emptyText: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
  },
  darkEmptyText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  fab: {
    position: "absolute",
    right: Spacing.SPACING.lg,
    bottom: Spacing.SPACING.lg,
    backgroundColor: Colors.PRIMARY,
    borderRadius: Spacing.BORDER_RADIUS.round,
    ...Spacing.SHADOW.lg,
  },
  dialogActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
});

export default HomeScreen;
