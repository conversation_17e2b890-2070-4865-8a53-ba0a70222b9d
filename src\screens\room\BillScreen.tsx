import React, { useState, useMemo } from "react";
import { View, StyleSheet, ScrollView, StatusBar } from "react-native";
import {
  Text,
  <PERSON>ton,
  Card,
  Appbar,
  Divider,
  IconButton,
  ActivityIndicator,
  Snackbar,
} from "react-native-paper";
import { OptimizedTextInput } from "../../components/OptimizedTextInput";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors } from "../../theme";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";
import {
  addBill,
  getBillById,
  getBillsByRoomId,
  updateBill,
  deleteBill,
  getLatestChiSoDienNuocCu,
} from "../../services/BillServices";
import { getRoomById } from "../../services/roomService";
import { HoaDon, AdditionalFee } from "../../types/models";
import {
  useDefaultPrices,
  DefaultService,
} from "../../context/DefaultPricesContext";

type BillScreenRouteProp = RouteProp<RootStackParamList, "BillScreen">;

const BillScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute<BillScreenRouteProp>();
  const { isDarkMode } = useTheme();
  const { t } = useLanguage();
  const { defaultElectricityPrice, defaultWaterPrice, defaultServices } =
    useDefaultPrices();

  const { billId, roomId, isEdit = false } = route.params || {};

  const [date, setDate] = useState("07/06/2025");
  const [oldElectricityReading, setOldElectricityReading] = useState("0");
  const [oldWaterReading, setOldWaterReading] = useState("0");
  const [newElectricityReading, setNewElectricityReading] = useState("0");
  const [newWaterReading, setNewWaterReading] = useState("0");
  const [electricityPrice, setElectricityPrice] = useState(
    defaultElectricityPrice
  );
  const [waterPrice, setWaterPrice] = useState(defaultWaterPrice);
  const [roomPrice, setRoomPrice] = useState("1500000");
  const [notes, setNotes] = useState("");
  const [paymentStatus, setPaymentStatus] = useState<
    "pending" | "paid" | "overdue"
  >("pending");

  // Convert default services to the format expected by BillScreen
  const [services, setServices] = useState(
    defaultServices.map((service, index) => ({
      id: parseInt(service.id) || index + 1,
      name: service.name,
      price: service.price,
    }))
  );

  const [bills, setBills] = useState<HoaDon[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentBill, setCurrentBill] = useState<HoaDon | null>(null);
  const [motelId, setMotelId] = useState<string>("");

  const electricityUsage = useMemo(() => {
    return Math.max(
      0,
      parseInt(newElectricityReading) - parseInt(oldElectricityReading)
    );
  }, [newElectricityReading, oldElectricityReading]);

  const waterUsage = useMemo(() => {
    return Math.max(0, parseInt(newWaterReading) - parseInt(oldWaterReading));
  }, [newWaterReading, oldWaterReading]);

  const electricityTotal = useMemo(() => {
    return electricityUsage * parseInt(electricityPrice);
  }, [electricityUsage, electricityPrice]);

  const waterTotal = useMemo(() => {
    return waterUsage * parseInt(waterPrice);
  }, [waterUsage, waterPrice]);

  const servicesTotal = useMemo(() => {
    return services.reduce(
      (total, service) => total + parseInt(service.price),
      0
    );
  }, [services]);

  const grandTotal = useMemo(() => {
    return electricityTotal + waterTotal + parseInt(roomPrice) + servicesTotal;
  }, [electricityTotal, waterTotal, roomPrice, servicesTotal]);

  const handleServicePriceChange = (serviceId: number, price: string) => {
    setServices(
      services.map((service) =>
        service.id === serviceId ? { ...service, price } : service
      )
    );
  };

  const handleServiceNameChange = (serviceId: number, name: string) => {
    setServices(
      services.map((service) =>
        service.id === serviceId ? { ...service, name } : service
      )
    );
  };

  const addNewService = () => {
    const newId = Math.max(...services.map((s) => s.id)) + 1;
    setServices([
      ...services,
      {
        id: newId,
        name: "",
        price: "0",
      },
    ]);
  };

  const removeService = (serviceId: number) => {
    setServices(services.filter((service) => service.id !== serviceId));
  };

  // Update services when default services change (only for new bills, not when editing)
  React.useEffect(() => {
    if (!isEdit) {
      setServices(
        defaultServices.map((service, index) => ({
          id: parseInt(service.id) || index + 1,
          name: service.name,
          price: service.price,
        }))
      );
    }
  }, [defaultServices, isEdit]);

  // Fetch bills for the room
  React.useEffect(() => {
    if (roomId) {
      setLoading(true);
      getBillsByRoomId(roomId)
        .then(setBills)
        .catch(() => setError("Lỗi khi tải danh sách hóa đơn"))
        .finally(() => setLoading(false));
    }
    if (billId && isEdit) {
      setLoading(true);
      getBillById(billId)
        .then((bill) => {
          if (bill) setCurrentBill(bill);
        })
        .catch(() => setError("Lỗi khi tải hóa đơn"))
        .finally(() => setLoading(false));
    }
  }, [roomId, billId, isEdit]);

  // Lấy giá phòng tự động khi có roomId
  React.useEffect(() => {
    if (roomId) {
      getRoomById(roomId).then((room) => {
        if (room && room.gia) {
          setRoomPrice(room.gia.toString());
        }
        if (room && room.motel_id) {
          setMotelId(room.motel_id);
        }
      });
    }
  }, [roomId]);

  // Thay thế logic lấy chỉ số điện nước khi tạo hóa đơn mới hoặc edit
  React.useEffect(() => {
    if (roomId && date) {
      getLatestChiSoDienNuocCu(roomId, date).then(
        ({ chi_so_dien_cu, chi_so_nuoc_cu }) => {
          setOldElectricityReading(chi_so_dien_cu?.toString() || "0");
          setOldWaterReading(chi_so_nuoc_cu?.toString() || "0");
        }
      );
    }
  }, [roomId, date, isEdit, currentBill]);

  // Khi vào chế độ Edit, đồng bộ dữ liệu hóa đơn vào form
  React.useEffect(() => {
    if (isEdit && currentBill) {
      setDate(currentBill.thang_hoa_don || "");
      setRoomPrice(currentBill.tienphong?.toString() || "");
      setElectricityPrice(
        currentBill.giadien !== undefined && currentBill.giadien !== null
          ? currentBill.giadien.toString()
          : ""
      ); // lấy đúng giá điện, tránh undefined
      setWaterPrice(
        currentBill.gianuoc !== undefined && currentBill.gianuoc !== null
          ? currentBill.gianuoc.toString()
          : ""
      ); // lấy đúng giá nước, tránh undefined
      setNotes(""); // Nếu có trường notes thì lấy từ currentBill
      if (
        currentBill.additional_fees &&
        currentBill.additional_fees.length > 0
      ) {
        setServices(
          currentBill.additional_fees.map((fee, idx) => ({
            id: idx + 1,
            name: fee.name,
            price: fee.amount.toString(),
          }))
        );
      }
      // Autofill chỉ số điện nước khi edit hóa đơn
      setOldElectricityReading(currentBill.chi_so_dien_cu?.toString() || "0");
      setNewElectricityReading(currentBill.chi_so_dien_moi?.toString() || "0");
      setOldWaterReading(currentBill.chi_so_nuoc_cu?.toString() || "0");
      setNewWaterReading(currentBill.chi_so_nuoc_moi?.toString() || "0");
    }
  }, [isEdit, currentBill, roomId]);

  // Convert UI state to HoaDon object for add/update
  const buildBillData = (): Partial<HoaDon> => {
    // Đảm bảo không truyền undefined cho các trường số
    const chi_so_dien_cu =
      oldElectricityReading !== undefined && oldElectricityReading !== null
        ? Number(oldElectricityReading)
        : 0;
    const chi_so_dien_moi =
      newElectricityReading !== undefined && newElectricityReading !== null
        ? Number(newElectricityReading)
        : 0;
    const chi_so_nuoc_cu =
      oldWaterReading !== undefined && oldWaterReading !== null
        ? Number(oldWaterReading)
        : 0;
    const chi_so_nuoc_moi =
      newWaterReading !== undefined && newWaterReading !== null
        ? Number(newWaterReading)
        : 0;
    return {
      phong_id: roomId,
      motel_id: motelId, // luôn truyền đúng motel_id
      tenant_id: "", // Cần truyền đúng tenant_id nếu có
      thang_hoa_don: date,
      tienphong: parseInt(roomPrice),
      tiendien: electricityTotal,
      tiennuoc: waterTotal,
      giadien: parseInt(electricityPrice), // thêm giá điện
      gianuoc: parseInt(waterPrice), // thêm giá nước
      additional_fees: services.map((s) => ({
        name: s.name,
        amount: parseInt(s.price),
      })),
      tong_tien: grandTotal,
      tiennuoc_moi: 0, // Nếu có logic riêng thì cập nhật
      auto_generated: false,
      due_date: new Date(),
      overdue_days: 0,
      payment_status: paymentStatus || "pending",
      chi_so_dien_cu,
      chi_so_dien_moi,
      chi_so_nuoc_cu,
      chi_so_nuoc_moi,
    };
  };

  // Thêm hoặc cập nhật hóa đơn
  const handleSaveBill = async () => {
    setLoading(true);
    setError(null);
    try {
      const billData = buildBillData();
      if (isEdit && billId) {
        const ok = await updateBill(billId, billData);
        if (ok) setSuccess("Cập nhật hóa đơn thành công!");
        else setError("Cập nhật hóa đơn thất bại!");
      } else {
        const res = await addBill(billData);
        if (res.success) setSuccess("Tạo hóa đơn thành công!");
        else setError("Tạo hóa đơn thất bại!");
      }
      navigation.goBack();
    } catch (e) {
      setError("Có lỗi xảy ra!");
    } finally {
      setLoading(false);
    }
  };

  // Xóa hóa đơn
  const handleDeleteBill = async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const ok = await deleteBill(id);
      if (ok) setSuccess("Xóa hóa đơn thành công!");
      else setError("Xóa hóa đơn thất bại!");
      // Refresh list
      setBills(await getBillsByRoomId(roomId));
    } catch (e) {
      setError("Có lỗi xảy ra khi xóa!");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + " VNĐ";
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {loading && (
        <ActivityIndicator
          animating
          size="large"
          style={{ margin: 16 }}
          color={Colors.PRIMARY}
        />
      )}
      <Snackbar
        visible={!!error || !!success}
        onDismiss={() => {
          setError(null);
          setSuccess(null);
        }}
        duration={3000}
        style={{ backgroundColor: error ? "#F44336" : "#4CAF50" }}
      >
        {error || success}
      </Snackbar>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />

      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content
          title={isEdit ? "Chỉnh sửa hóa đơn" : "Tạo hóa đơn mới"}
          titleStyle={styles.headerTitle}
        />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={[styles.card, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <Text style={[styles.cardTitle, isDarkMode && styles.darkText]}>
              Thông tin hóa đơn
            </Text>
            <OptimizedTextInput
              label="Ngày tạo hóa đơn"
              value={date}
              onChangeText={setDate}
              style={styles.input}
              leftIcon="calendar"
            />
          </Card.Content>
        </Card>

        <Card style={[styles.card, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <Text style={[styles.cardTitle, isDarkMode && styles.darkText]}>
              Chỉ số điện nước
            </Text>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Chỉ số điện cũ"
                  value={oldElectricityReading}
                  onChangeText={setOldElectricityReading}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Chỉ số điện mới"
                  value={newElectricityReading}
                  onChangeText={setNewElectricityReading}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Chỉ số nước cũ"
                  value={oldWaterReading}
                  onChangeText={setOldWaterReading}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Chỉ số nước mới"
                  value={newWaterReading}
                  onChangeText={setNewWaterReading}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.usageContainer}>
              <View style={styles.usageItem}>
                <MaterialCommunityIcons
                  name="flash"
                  size={24}
                  color={Colors.PRIMARY}
                />
                <Text style={[styles.usageText, isDarkMode && styles.darkText]}>
                  Điện tiêu thụ: {electricityUsage} kWh
                </Text>
              </View>
              <View style={styles.usageItem}>
                <MaterialCommunityIcons
                  name="water"
                  size={24}
                  color={Colors.PRIMARY}
                />
                <Text style={[styles.usageText, isDarkMode && styles.darkText]}>
                  Nước tiêu thụ: {waterUsage} m³
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        <Card style={[styles.card, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <Text style={[styles.cardTitle, isDarkMode && styles.darkText]}>
              Đơn giá & Tiền phòng
            </Text>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Giá điện (VNĐ/kWh)"
                  value={electricityPrice}
                  onChangeText={setElectricityPrice}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
              <View style={styles.halfWidth}>
                <OptimizedTextInput
                  label="Giá nước (VNĐ/m³)"
                  value={waterPrice}
                  onChangeText={setWaterPrice}
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
            </View>

            <OptimizedTextInput
              label="Tiền phòng (VNĐ)"
              value={roomPrice}
              onChangeText={setRoomPrice}
              keyboardType="numeric"
              style={styles.input}
            />
          </Card.Content>
        </Card>

        <Card style={[styles.card, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <View style={styles.serviceHeader}>
              <Text style={[styles.cardTitle, isDarkMode && styles.darkText]}>
                Dịch vụ thêm
              </Text>
              <Button
                mode="outlined"
                onPress={() => {
                  setServices(
                    defaultServices.map((service, index) => ({
                      id: parseInt(service.id) || index + 1,
                      name: service.name,
                      price: service.price,
                    }))
                  );
                }}
                style={styles.resetButton}
                compact
              >
                {t("profile.resetToDefault")}
              </Button>
            </View>

            {services.map((service) => (
              <View key={service.id} style={styles.serviceRow}>
                <View style={styles.serviceNameContainer}>
                  <OptimizedTextInput
                    label="Tên dịch vụ"
                    value={service.name}
                    onChangeText={(name) =>
                      handleServiceNameChange(service.id, name)
                    }
                    style={styles.serviceNameInput}
                    placeholder="Nhập tên dịch vụ..."
                  />
                </View>
                <View style={styles.servicePriceContainer}>
                  <OptimizedTextInput
                    label="Giá (VNĐ)"
                    value={service.price}
                    onChangeText={(price) =>
                      handleServicePriceChange(service.id, price)
                    }
                    keyboardType="numeric"
                    style={styles.servicePriceInput}
                  />
                </View>
                {services.length > 1 && (
                  <IconButton
                    icon="delete"
                    onPress={() => removeService(service.id)}
                    style={styles.removeIconButton}
                    iconColor="#F44336"
                    size={20}
                  />
                )}
              </View>
            ))}

            <Button
              mode="outlined"
              onPress={addNewService}
              style={styles.addServiceButton}
              icon="plus"
              textColor={Colors.PRIMARY}
            >
              Thêm dịch vụ
            </Button>
          </Card.Content>
        </Card>

        <Card style={[styles.card, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <Text style={[styles.cardTitle, isDarkMode && styles.darkText]}>
              Ghi chú
            </Text>
            <OptimizedTextInput
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Thêm ghi chú cho hóa đơn..."
              leftIcon="note-text"
            />
          </Card.Content>
        </Card>

        <Card style={[styles.summaryCard, isDarkMode && styles.darkCard]}>
          <Card.Content>
            <Text style={[styles.summaryTitle, isDarkMode && styles.darkText]}>
              Tổng kết hóa đơn
            </Text>

            <View style={styles.summaryRow}>
              <Text
                style={[styles.summaryLabel, isDarkMode && styles.darkText]}
              >
                Tiền điện:
              </Text>
              <Text
                style={[styles.summaryValue, isDarkMode && styles.darkText]}
              >
                {formatCurrency(electricityTotal)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text
                style={[styles.summaryLabel, isDarkMode && styles.darkText]}
              >
                Tiền nước:
              </Text>
              <Text
                style={[styles.summaryValue, isDarkMode && styles.darkText]}
              >
                {formatCurrency(waterTotal)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text
                style={[styles.summaryLabel, isDarkMode && styles.darkText]}
              >
                Tiền phòng:
              </Text>
              <Text
                style={[styles.summaryValue, isDarkMode && styles.darkText]}
              >
                {formatCurrency(parseInt(roomPrice))}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text
                style={[styles.summaryLabel, isDarkMode && styles.darkText]}
              >
                Dịch vụ:
              </Text>
              <Text
                style={[styles.summaryValue, isDarkMode && styles.darkText]}
              >
                {formatCurrency(servicesTotal)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.totalRow}>
              <Text style={[styles.totalLabel, isDarkMode && styles.darkText]}>
                TỔNG CỘNG:
              </Text>
              <Text style={[styles.totalValue, isDarkMode && styles.darkText]}>
                {formatCurrency(grandTotal)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleSaveBill}
          style={styles.saveButton}
          buttonColor={Colors.PRIMARY}
          icon={isEdit ? "content-save" : "plus"}
        >
          {isEdit ? "Cập nhật hóa đơn" : "Tạo hóa đơn"}
        </Button>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  darkContainer: {
    backgroundColor: "#121212",
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontSize: 18,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  darkCard: {
    backgroundColor: "#1E1E1E",
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 16,
    color: Colors.TEXT_PRIMARY,
  },
  darkText: {
    color: Colors.WHITE,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  divider: {
    marginVertical: 16,
  },
  usageContainer: {
    marginTop: 8,
  },
  usageItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  usageText: {
    marginLeft: 12,
    fontSize: 14,
    fontWeight: "500",
  },
  serviceRow: {
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 12,
    gap: 8,
  },
  serviceNameContainer: {
    width: "55%",
  },
  servicePriceContainer: {
    width: "30%",
  },
  serviceNameInput: {
    marginBottom: 0,
  },
  servicePriceInput: {
    marginBottom: 0,
  },
  removeIconButton: {
    marginBottom: 4,
  },
  addServiceButton: {
    marginTop: 16,
    borderColor: Colors.PRIMARY,
  },
  summaryCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 3,
    backgroundColor: "#F8F9FA",
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.PRIMARY,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.PRIMARY,
  },
  saveButton: {
    marginTop: 8,
    marginBottom: 32,
    borderRadius: 8,
  },
  serviceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  resetButton: {
    borderColor: Colors.PRIMARY,
  },
});

export default BillScreen;
