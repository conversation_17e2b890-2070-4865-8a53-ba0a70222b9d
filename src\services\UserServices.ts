import { collection, doc, getDoc, getDocs, query, setDoc, updateDoc, where, deleteDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';

/**
 * L<PERSON>y UID từ email bằng cách truy vấn collection 'users' (mỗi document chứa email và uid)
 * @param email Email của user
 * @returns UID nếu tìm thấy, null nếu không
 */
export const getUidByEmail = async (email: string): Promise<string | null> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email));
    const snapshot = await getDocs(q);
    if (snapshot.empty) return null;
    // <PERSON><PERSON><PERSON> sử mỗi email là duy nhất
    const userDoc = snapshot.docs[0];
    return userDoc.data().uid || userDoc.id;
  } catch (error) {
    console.error('Lỗi khi lấy UID từ email:', error);
    return null;
  }
};

// Thêm user mới (nếu chưa tồn tại)
export const addUser = async (user: { uid: string; email: string; displayName: string; phone: string }): Promise<boolean> => {
  try {
    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);
    if (!userSnap.exists()) {
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        phone: user.phone,
        createdAt: new Date(),
      });
    }
    return true;
  } catch (error) {
    console.error('Lỗi khi thêm user:', error);
    return false;
  }
};

// Lấy user theo UID
export const getUserById = async (uid: string): Promise<any | null> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);
    if (!userSnap.exists()) return null;
    const data = userSnap.data();
    // Đảm bảo trả về cả avatar (nếu có)
    return {
      ...data,
      avatar: data.avatar || null,
    };
  } catch (error) {
    console.error('Lỗi khi lấy user:', error);
    return null;
  }
};

// Cập nhật user (thêm avatar)
export const updateUser = async (
  uid: string,
  data: { displayName?: string; phone?: string; email?: string; avatar?: string }
): Promise<boolean> => {
  try {
    const userRef = doc(db, 'users', uid);
    await updateDoc(userRef, {
      ...data,
      updatedAt: new Date(),
    });
    return true;
  } catch (error) {
    console.error('Lỗi khi cập nhật user:', error);
    return false;
  }
};

// Xóa user
export const deleteUser = async (uid: string): Promise<boolean> => {
  try {
    const userRef = doc(db, 'users', uid);
    await deleteDoc(userRef);
    return true;
  } catch (error) {
    console.error('Lỗi khi xóa user:', error);
    return false;
  }
};
