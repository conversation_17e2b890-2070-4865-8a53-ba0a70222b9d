import { collection, getDocs, query, where, addDoc, serverTimestamp, doc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { HoaDon } from '../types';
import { getRoomStatsByMotel } from './roomService';
import { addLog } from "./logService";

const billsRef = collection(db, 'bills');

// Thêm hóa đơn mới
export const addBill = async (billData: Partial<HoaDon>): Promise<{ success: boolean; id?: string; error?: any }> => {
  try {
    if (!billData.motel_id) {
      console.error('[addBill] Thiếu motel_id khi tạo hóa đơn!');
      return { success: false, error: 'Thiếu motel_id' };
    }
    const docRef = await addDoc(billsRef, {
      phong_id: billData.phong_id,
      motel_id: billData.motel_id, // luôn truyền motel_id
      tenant_id: billData.tenant_id || "",
      thang_hoa_don: billData.thang_hoa_don,
      tienphong: Number(billData.tienphong) || 0,
      tiendien: Number(billData.tiendien) || 0,
      tiennuoc: Number(billData.tiennuoc) || 0,
      giadien: Number(billData.giadien) || 0, // thêm giá điện
      gianuoc: Number(billData.gianuoc) || 0, // thêm giá nước
      additional_fees: billData.additional_fees || [],
      tong_tien: Number(billData.tong_tien) || 0,
      tiennuoc_moi: Number(billData.tiennuoc_moi) || 0,
      chi_so_dien_cu: Number(billData.chi_so_dien_cu) || 0,
      chi_so_dien_moi: Number(billData.chi_so_dien_moi) || 0,
      chi_so_nuoc_cu: Number(billData.chi_so_nuoc_cu) || 0,
      chi_so_nuoc_moi: Number(billData.chi_so_nuoc_moi) || 0,
      auto_generated: billData.auto_generated || false,
      created_at: serverTimestamp(),
      due_date: billData.due_date || null,
      overdue_days: billData.overdue_days || 0,
      payment_status: billData.payment_status ?? 'pending',
    });
    // Ghi log thêm hóa đơn
    await addLog({
      action: "add",
      targetId: docRef.id,
      targetType: "bill",
      motelId: billData.motel_id,
      after: billData,
      description: `Thêm hóa đơn tháng ${billData.thang_hoa_don || ''} cho phòng ${billData.phong_id}`,
    });
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Lỗi khi thêm hóa đơn:', error);
    return { success: false, error };
  }
};

// Lấy hóa đơn theo ID
export const getBillById = async (billId: string): Promise<HoaDon | null> => {
  try {
    const billDoc = await getDoc(doc(billsRef, billId));
    if (!billDoc.exists()) return null;
    const data = billDoc.data();
    return {
      hoadon_id: billDoc.id,
      phong_id: data.phong_id,
      motel_id: data.motel_id,
      tenant_id: data.tenant_id,
      thang_hoa_don: data.thang_hoa_don,
      tienphong: data.tienphong,
      tiendien: data.tiendien,
      tiennuoc: data.tiennuoc,
      giadien: data.giadien, // lấy giá điện từ db
      gianuoc: data.gianuoc, // lấy giá nước từ db
      additional_fees: data.additional_fees || [],
      tong_tien: data.tong_tien,
      tiennuoc_moi: data.tiennuoc_moi,
      // Thêm chỉ số điện nước cũ và mới
      chi_so_dien_cu: data.chi_so_dien_cu,
      chi_so_dien_moi: data.chi_so_dien_moi,
      chi_so_nuoc_cu: data.chi_so_nuoc_cu,
      chi_so_nuoc_moi: data.chi_so_nuoc_moi,
      auto_generated: data.auto_generated || false,
      created_at: data.created_at?.toDate() || new Date(),
      due_date: data.due_date?.toDate() || new Date(),
      overdue_days: data.overdue_days || 0,
      payment_status: data.payment_status || 'pending',
    } as HoaDon;
  } catch (error) {
    console.error('Lỗi khi lấy hóa đơn:', error);
    return null;
  }
};

// Lấy danh sách hóa đơn theo roomId
export const getBillsByRoomId = async (roomId: string): Promise<HoaDon[]> => {
  try {
    const q = query(billsRef, where('phong_id', '==', roomId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        hoadon_id: doc.id,
        phong_id: data.phong_id,
        motel_id: data.motel_id,
        tenant_id: data.tenant_id,
        thang_hoa_don: data.thang_hoa_don,
        tienphong: data.tienphong,
        tiendien: data.tiendien,
        tiennuoc: data.tiennuoc,
        additional_fees: data.additional_fees || [],
        tong_tien: data.tong_tien,
        tiennuoc_moi: data.tiennuoc_moi,
        // Thêm chỉ số điện nước cũ và mới
        chi_so_dien_cu: data.chi_so_dien_cu,
        chi_so_dien_moi: data.chi_so_dien_moi,
        chi_so_nuoc_cu: data.chi_so_nuoc_cu,
        chi_so_nuoc_moi: data.chi_so_nuoc_moi,
        auto_generated: data.auto_generated || false,
        created_at: data.created_at?.toDate() || new Date(),
        due_date: data.due_date?.toDate() || new Date(),
        overdue_days: data.overdue_days || 0,
        payment_status: data.payment_status || 'pending',
      } as HoaDon;
    });
  } catch (error) {
    console.error('Lỗi khi lấy danh sách hóa đơn:', error);
    return [];
  }
};

// Cập nhật hóa đơn
export const updateBill = async (billId: string, billData: Partial<HoaDon>): Promise<boolean> => {
  try {
    if (!billData.motel_id) {
      console.error('[updateBill] Thiếu motel_id khi cập nhật hóa đơn!');
      return false;
    }
    const billRef = doc(billsRef, billId);
    // Lấy dữ liệu trước khi update
    const beforeDoc = await getDoc(billRef);
    const before = beforeDoc.exists() ? beforeDoc.data() : undefined;
    await updateDoc(billRef, {
      ...billData,
      motel_id: billData.motel_id, // luôn cập nhật motel_id
      giadien: billData.giadien, // thêm giá điện
      gianuoc: billData.gianuoc, // thêm giá nước
      updatedAt: serverTimestamp(),
    });
    // Ghi log sửa hóa đơn
    await addLog({
      action: "edit",
      targetId: billId,
      targetType: "bill",
      motelId: billData.motel_id,
      before,
      after: billData,
      description: `Cập nhật hóa đơn tháng ${billData.thang_hoa_don || before?.thang_hoa_don || ''} cho phòng ${billData.phong_id || before?.phong_id}`,
    });
    return true;
  } catch (error) {
    console.error('Lỗi khi cập nhật hóa đơn:', error);
    return false;
  }
};

// Xóa hóa đơn
export const deleteBill = async (billId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(billsRef, billId));
    return true;
  } catch (error) {
    console.error('Lỗi khi xóa hóa đơn:', error);
    return false;
  }
};

// Lấy chỉ số điện nước cũ (gần nhất trước tháng hóa đơn) dựa trên hóa đơn đã tạo
export const getLatestChiSoDienNuocCu = async (roomId: string, billMonth: string): Promise<{
  chi_so_dien_cu: number | null;
  chi_so_nuoc_cu: number | null;
  thang_hoa_don_moi?: string;
  chi_so_dien_moi?: number | null;
  chi_so_nuoc_moi?: number | null;
}> => {
  try {
    // Lấy các hóa đơn (bills) của phòng
    const q = query(billsRef, where('phong_id', '==', roomId));
    const snapshot = await getDocs(q);
    if (snapshot.empty) return { chi_so_dien_cu: null, chi_so_nuoc_cu: null };
    // Lọc các hóa đơn trước tháng hóa đơn hiện tại
    const bills = snapshot.docs
      .map(doc => ({
        ...doc.data(),
        thang_hoa_don: doc.data().thang_hoa_don,
        chi_so_dien_moi: doc.data().chi_so_dien_moi,
        chi_so_nuoc_moi: doc.data().chi_so_nuoc_moi
      }))
      .filter(bill => bill.thang_hoa_don && bill.thang_hoa_don < billMonth)
      .sort((a, b) => b.thang_hoa_don.localeCompare(a.thang_hoa_don)); // Giảm dần, bản ghi gần nhất đầu tiên
    if (bills.length === 0) return { chi_so_dien_cu: null, chi_so_nuoc_cu: null };
    const result = {
      chi_so_dien_cu: bills[0].chi_so_dien_moi ?? null,
      chi_so_nuoc_cu: bills[0].chi_so_nuoc_moi ?? null,
      thang_hoa_don_moi: bills[0].thang_hoa_don ?? null,
      chi_so_dien_moi: bills[0].chi_so_dien_moi ?? null,
      chi_so_nuoc_moi: bills[0].chi_so_nuoc_moi ?? null,
    };
    return result;
  } catch (error) {
    console.error('Lỗi khi lấy chỉ số điện nước cũ từ hóa đơn:', error);
    return { chi_so_dien_cu: null, chi_so_nuoc_cu: null };
  }
};

// Cập nhật trạng thái thanh toán hóa đơn
export const updateBillPaymentStatus = async (billId: string, payment_status: 'pending' | 'paid' | 'overdue'): Promise<boolean> => {
  try {
    const billRef = doc(billsRef, billId);
    // Lấy dữ liệu trước khi update
    const beforeDoc = await getDoc(billRef);
    const before = beforeDoc.exists() ? beforeDoc.data() : undefined;
    await updateDoc(billRef, { payment_status });
    // Ghi log thu tiền hóa đơn
    if (before && before.motel_id) {
      await addLog({
        action: "collect_payment",
        targetId: billId,
        targetType: "bill",
        motelId: before.motel_id,
        before,
        after: { ...before, payment_status },
        description: `Thu tiền hóa đơn tháng ${before.thang_hoa_don || ''} cho phòng ${before.phong_id}`,
      });
    }
    return true;
  } catch (error) {
    console.error('Lỗi khi cập nhật trạng thái thanh toán:', error);
    return false;
  }
};

// Tính tổng doanh thu của 1 hoặc nhiều motel dựa trên payment_status là 'paid'
export const getTotalRevenue = async (motelIds: string | string[]): Promise<number> => {
  try {
    // Đảm bảo motelIds là mảng
    const ids = Array.isArray(motelIds) ? motelIds : [motelIds];
    // Truy vấn các hóa đơn có motel_id thuộc ids và payment_status là 'paid'
    const q = query(
      billsRef,
      where('motel_id', 'in', ids),
      where('payment_status', '==', 'paid')
    );
    const snapshot = await getDocs(q);
    let total = 0;
    snapshot.forEach(doc => {
      const data = doc.data();
      total += data.tong_tien || 0;
    });
    return total;
  } catch (error) {
    console.error('Lỗi khi tính tổng doanh thu:', error);
    return 0;
  }
};

// Tính tổng số tiền chưa thanh toán của 1 hoặc nhiều motel (payment_status != 'paid')
export const getTotalUnpaid = async (motelIds: string | string[]): Promise<number> => {
  try {
    const ids = Array.isArray(motelIds) ? motelIds : [motelIds];
    // Truy vấn các hóa đơn có motel_id thuộc ids và payment_status khác 'paid'
    const q = query(
      billsRef,
      where('motel_id', 'in', ids),
      where('payment_status', 'in', ['pending', 'overdue'])
    );
    const snapshot = await getDocs(q);
    let total = 0;
    snapshot.forEach(doc => {
      const data = doc.data();
      total += data.tong_tien || 0;
    });
    return total;
  } catch (error) {
    console.error('Lỗi khi tính tổng tiền chưa thanh toán:', error);
    return 0;
  }
};

// Lấy doanh thu của 1 hoặc nhiều motel trong 1 tháng cụ thể (theo payment_status = 'paid')
export const getMonthlyRevenue = async (
  motelIds: string | string[],
  month: number, // 1-12
  year: number
): Promise<number> => {
  try {
    const ids = Array.isArray(motelIds) ? motelIds : [motelIds];
    const q = query(
      billsRef,
      where('motel_id', 'in', ids),
      where('payment_status', '==', 'paid')
    );
    const snapshot = await getDocs(q);
    let total = 0;
    snapshot.forEach(doc => {
      const data = doc.data();
      let billDate: Date | null = null;
      let reason = '';
      if (data.thang_hoa_don) {
        if (/^\d{2}\/\d{2}\/\d{4}$/.test(data.thang_hoa_don)) {
          // DD/MM/YYYY
          const [d, m, y] = data.thang_hoa_don.split('/').map(Number);
          billDate = new Date(y, m - 1, d);
        } else if (/^\d{4}-\d{2}-\d{2}$/.test(data.thang_hoa_don)) {
          // YYYY-MM-DD
          const [y, m, d] = data.thang_hoa_don.split('-').map(Number);
          billDate = new Date(y, m - 1, d);
        } else {
          reason = 'Sai định dạng ngày';
        }
      } else {
        reason = 'Không có thang_hoa_don';
      }
      if (
        billDate &&
        billDate.getFullYear() === year &&
        billDate.getMonth() + 1 === month
      ) {
        total += data.tong_tien || 0;
        reason = 'Được cộng vào doanh thu';
      } else if (!reason) {
        reason = 'Không khớp tháng/năm';
      }
      console.log('[DEBUG][getMonthlyRevenue] Bill:', {
        id: doc.id,
        thang_hoa_don: data.thang_hoa_don,
        billDate: billDate ? billDate.toISOString().slice(0, 10) : null,
        tong_tien: data.tong_tien,
        reason,
        year,
        month
      });
    });
    console.log('[DEBUG][getMonthlyRevenue] Tổng doanh thu:', total, 'cho tháng', month, year);
    return total;
  } catch (error) {
    console.error('Lỗi khi tính doanh thu tháng:', error);
    return 0;
  }
};

/**
 * Tính doanh thu trung bình theo tháng (chỉ tính các bill đã thanh toán)
 */
export const getAverageRevenueByMonth = async (motelId: string, month: number, year: number): Promise<number> => {
  const total = await getMonthlyRevenue(motelId, month, year);
  // Đếm số bill đã thanh toán trong tháng
  const ids = [motelId];
  const q = query(
    billsRef,
    where('motel_id', 'in', ids),
    where('payment_status', '==', 'paid')
  );
  const snapshot = await getDocs(q);
  let count = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    let billDate: Date | null = null;
    if (data.thang_hoa_don) {
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(data.thang_hoa_don)) {
        const [d, m, y] = data.thang_hoa_don.split('/').map(Number);
        billDate = new Date(y, m - 1, d);
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(data.thang_hoa_don)) {
        const [y, m, d] = data.thang_hoa_don.split('-').map(Number);
        billDate = new Date(y, m - 1, d);
      }
    }
    if (billDate && billDate.getFullYear() === year && billDate.getMonth() + 1 === month) {
      count++;
    }
  });
  return count > 0 ? total / count : 0;
};

/**
 * Tính tổng số tiền chưa thanh toán trong tháng (pending + overdue)
 */
export const getTotalUnpaidByMonth = async (motelId: string, month: number, year: number): Promise<number> => {
  const ids = [motelId];
  const q = query(
    billsRef,
    where('motel_id', 'in', ids),
    where('payment_status', 'in', ['pending', 'overdue'])
  );
  const snapshot = await getDocs(q);
  let total = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    let billDate: Date | null = null;
    if (data.thang_hoa_don) {
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(data.thang_hoa_don)) {
        const [d, m, y] = data.thang_hoa_don.split('/').map(Number);
        billDate = new Date(y, m - 1, d);
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(data.thang_hoa_don)) {
        const [y, m, d] = data.thang_hoa_don.split('-').map(Number);
        billDate = new Date(y, m - 1, d);
      }
    }
    if (billDate && billDate.getFullYear() === year && billDate.getMonth() + 1 === month) {
      total += data.tong_tien || 0;
    }
  });
  return total;
};

/**
 * Tính tỉ lệ thanh toán đúng hạn trong tháng (số bill paid trước hoặc đúng due_date / tổng bill tháng đó)
 */
export const getOnTimePaymentRateByMonth = async (motelId: string, month: number, year: number): Promise<number> => {
  const ids = [motelId];
  const q = query(
    billsRef,
    where('motel_id', 'in', ids)
  );
  const snapshot = await getDocs(q);
  let total = 0;
  let onTime = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    let billDate: Date | null = null;
    if (data.thang_hoa_don) {
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(data.thang_hoa_don)) {
        const [d, m, y] = data.thang_hoa_don.split('/').map(Number);
        billDate = new Date(y, m - 1, d);
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(data.thang_hoa_don)) {
        const [y, m, d] = data.thang_hoa_don.split('-').map(Number);
        billDate = new Date(y, m - 1, d);
      }
    }
    if (billDate && billDate.getFullYear() === year && billDate.getMonth() + 1 === month) {
      total++;
      if (data.payment_status === 'paid' && data.due_date && data.created_at) {
        const due = data.due_date.toDate ? data.due_date.toDate() : new Date(data.due_date);
        const paid = data.created_at.toDate ? data.created_at.toDate() : new Date(data.created_at);
        if (paid <= due) onTime++;
      }
    }
  });
  return total > 0 ? Math.round((onTime / total) * 100) : 0;
};

/**
 * Tính tỉ lệ cho thuê trên toàn bộ phòng trong tháng (số phòng có bill tháng đó / tổng số phòng)
 */
export const getRentalRateByMonth = async (motelId: string, month: number, year: number): Promise<number> => {
  // Lấy tổng số phòng
  const { total } = await getRoomStatsByMotel(motelId);
  if (total === 0) return 0;
  // Đếm số phòng có bill tháng đó
  const q = query(
    billsRef,
    where('motel_id', '==', motelId)
  );
  const snapshot = await getDocs(q);
  const roomSet = new Set<string>();
  snapshot.forEach(doc => {
    const data = doc.data();
    let billDate: Date | null = null;
    if (data.thang_hoa_don) {
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(data.thang_hoa_don)) {
        const [d, m, y] = data.thang_hoa_don.split('/').map(Number);
        billDate = new Date(y, m - 1, d);
      } else if (/^\d{4}-\d{2}-\d{2}$/.test(data.thang_hoa_don)) {
        const [y, m, d] = data.thang_hoa_don.split('-').map(Number);
        billDate = new Date(y, m - 1, d);
      }
    }
    if (billDate && billDate.getFullYear() === year && billDate.getMonth() + 1 === month) {
      roomSet.add(data.phong_id);
    }
  });
  return Math.round((roomSet.size / total) * 100);
};

/**
 * Tính tỉ lệ tăng trưởng doanh thu so với tháng trước (tăng trưởng %)
 */
export const getRevenueGrowthRate = async (motelId: string, month: number, year: number): Promise<number> => {
  const current = await getMonthlyRevenue(motelId, month, year);
  // Tính tháng trước
  let prevMonth = month - 1;
  let prevYear = year;
  if (prevMonth === 0) {
    prevMonth = 12;
    prevYear--;
  }
  const prev = await getMonthlyRevenue(motelId, prevMonth, prevYear);
  if (prev === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - prev) / prev) * 100);
};
