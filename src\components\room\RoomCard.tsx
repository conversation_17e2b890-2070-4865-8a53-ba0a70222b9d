import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Room, Resident } from "../../types";
import { Colors, Spacing, Typography } from "../../theme";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Surface, Chip } from "react-native-paper";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";

interface RoomCardProps {
  room: Room;
  residents?: Resident[];
  onPress?: () => void;
}

export default function RoomCard({
  room,
  residents = [],
  onPress,
}: RoomCardProps) {
  const { isDarkMode, colors } = useTheme();
  const { t } = useLanguage();

  const formatCurrency = (amount: number) => {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " đ";
  };

  const getStatusColor = (status: string) => {
    return Colors.getStatusColor(status);
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "occupied":
        return t("room.statusOccupied");
      case "available":
        return t("room.statusAvailable");
      case "maintenance":
        return t("room.statusMaintenance");
      default:
        return t("room.statusAll");
    }
  };

  return (
    <Surface
      style={[styles.container, isDarkMode && styles.darkContainer]}
      elevation={2}
    >
      <TouchableOpacity onPress={onPress} style={styles.touchable}>
        <View
          style={[
            styles.statusBar,
            { backgroundColor: getStatusColor(room.status) },
          ]}
        />
        <View style={styles.contentContainer}>
          <View style={styles.infoContainer}>
            <View style={styles.headerContainer}>
              <Text
                style={[styles.roomNumber, isDarkMode && styles.darkRoomNumber]}
                numberOfLines={1}
              >
                {t("room.roomLabel")} {room.room_number}
              </Text>
              <Chip
                style={[
                  styles.statusChip,
                  { backgroundColor: getStatusColor(room.status) },
                ]}
                textStyle={styles.statusChipText}
              >
                {getStatusText(room.status)}
              </Chip>
            </View>

            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <MaterialCommunityIcons
                  name="cash"
                  size={16}
                  color={
                    isDarkMode ? colors.TEXT_SECONDARY : Colors.TEXT_SECONDARY
                  }
                />
                <Text
                  style={[
                    styles.detailText,
                    isDarkMode && styles.darkDetailText,
                  ]}
                >
                  {formatCurrency(room.gia)}/{t("room.month")}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <MaterialCommunityIcons
                  name="ruler-square"
                  size={16}
                  color={
                    isDarkMode ? colors.TEXT_SECONDARY : Colors.TEXT_SECONDARY
                  }
                />
                <Text
                  style={[
                    styles.detailText,
                    isDarkMode && styles.darkDetailText,
                  ]}
                >
                  {room.dienTich} m²
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.residentsContainer}>
            <View style={styles.residentsCountContainer}>
              <Text
                style={[
                  styles.residentsCount,
                  isDarkMode && styles.darkResidentsCount,
                ]}
              >
                {residents.length}
              </Text>
              <MaterialCommunityIcons
                name="account-group"
                size={24}
                color={Colors.PRIMARY}
              />
            </View>
            {room.loaiPhong.length > 0 && (
              <Chip
                style={[
                  styles.roomTypeChip,
                  isDarkMode && styles.darkRoomTypeChip,
                ]}
                textStyle={[
                  styles.roomTypeChipText,
                  isDarkMode && styles.darkRoomTypeChipText,
                ]}
                compact
              >
                {t(room.loaiPhong[0]) || room.loaiPhong[0]}
              </Chip>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    maxWidth: 500,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    overflow: "hidden",
    backgroundColor: Colors.WHITE,
    ...Spacing.SHADOW.md,
    marginVertical: Spacing.SPACING.xs,
  },
  darkContainer: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  touchable: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusBar: {
    height: "100%",
    width: 6,
  },
  contentContainer: {
    flex: 1,
    flexDirection: "row",
    padding: Spacing.SPACING.lg,
    justifyContent: "space-between",
  },
  infoContainer: {
    flex: 1,
    justifyContent: "space-between",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Spacing.SPACING.xs,
  },
  roomNumber: {
    fontSize: Typography.FONT_SIZE.lg + 2,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    letterSpacing: 0.3,
  },
  darkRoomNumber: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  statusChip: {
    paddingHorizontal: Spacing.SPACING.md,
    paddingVertical: Spacing.SPACING.xs,
    borderRadius: Spacing.BORDER_RADIUS.lg,
  },
  statusChipText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.xs,
    fontWeight: Typography.FONT_WEIGHT.semibold,
    marginRight: 0,
    marginLeft: 0,
    marginTop: 0,
    marginBottom: 0,
  },
  detailsContainer: {
    marginTop: Spacing.SPACING.xs,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.SPACING.xs,
  },
  detailText: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    marginLeft: Spacing.SPACING.xs,
  },
  darkDetailText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  residentsContainer: {
    alignItems: "flex-end",
    justifyContent: "space-between",
    paddingLeft: Spacing.SPACING.sm,
  },
  residentsCountContainer: {
    alignItems: "center",
  },
  residentsCount: {
    fontSize: Typography.FONT_SIZE.lg,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.PRIMARY,
  },
  darkResidentsCount: {
    color: Colors.PRIMARY,
  },
  roomTypeChip: {
    backgroundColor: `${Colors.PRIMARY}15`,
    marginTop: Spacing.SPACING.sm,
    borderRadius: Spacing.BORDER_RADIUS.md,
  },
  darkRoomTypeChip: {
    backgroundColor: `${Colors.PRIMARY}25`,
  },
  roomTypeChipText: {
    fontSize: Typography.FONT_SIZE.xs,
    color: Colors.PRIMARY,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  darkRoomTypeChipText: {
    color: Colors.PRIMARY,
  },
});
