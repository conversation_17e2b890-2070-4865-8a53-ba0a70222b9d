// src/services/logService.ts
// Service for audit logging actions (add/edit/delete) to Firestore

import { getFirestore, collection, addDoc, query, where, getDocs, Timestamp } from "firebase/firestore";
import { auth } from "../../config/firebase";

export interface AuditLog {
  action: string; // e.g. 'add', 'edit', 'delete'
  targetId: string;
  targetType: string; // e.g. 'motel', 'room', etc.
  userId: string;
  userName: string;
  timestamp: Date;
  motelId: string; // Thêm trường motelId để truy vấn log theo motel
  before?: any;
  after?: any;
  description?: string;
}

const db = getFirestore();
const LOGS_COLLECTION = "audit_logs";

export const addLog = async (log: Omit<AuditLog, "timestamp" | "userId" | "userName"> & { before?: any; after?: any; description?: string; }) => {
  const user = auth.currentUser;
  if (!user) throw new Error("User not authenticated");
  if (!log.motelId) throw new Error("Missing motelId in log");
  const logData = {
    ...log,
    userId: user.uid,
    userName: user.displayName || user.email || "",
    timestamp: Timestamp.now(),
  };
  await addDoc(collection(db, LOGS_COLLECTION), logData);
};

export const getLogs = async (motelId: string) => {
  const q = query(
    collection(db, LOGS_COLLECTION),
    where("motelId", "==", motelId)
  );
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};
