import React, { useEffect } from "react";
import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from "@react-navigation/native";
import { Provider as PaperProvider } from "react-native-paper";
import { RootNavigator } from "./src/navigation";
import { createPaperTheme } from "./src/theme";
import { LanguageProvider } from "./src/context/LanguageContext";
import { ThemeProvider, useTheme } from "./src/context/ThemeContext";
import { StatusBar } from "react-native";
import { configureGoogleSignIn } from "./src/utils/googleAuthExpo";
import { MotelProvider, useMotel } from "./src/context/MotelContext";
import { auth } from "./config/firebase";
import { getFirstMotel, addMotel, initialMotel } from "./src/services/MotelServices";
import { getUserById } from "./src/services/UserServices";
import { DefaultPricesProvider } from "./src/context/DefaultPricesContext";

// AppContent component to access theme context
const AppContent = () => {
  const { isDarkMode } = useTheme();
  const { setMotelId } = useMotel();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      console.log('Auth state changed:', user);
      if (user) {
        const uid = user.uid;
        // Lấy thông tin user từ Firestore
        // Đoạn này dùng Debug
        /*
        const userData = await getUserById(uid);
        if (userData) {
          console.log("User displayName from Firestore:", userData.displayName);
        }
        */
        const firstMotel = await getFirstMotel(uid);
        //console.log('First motel:', firstMotel);
        if (firstMotel === 0) {
          // Chưa có motel nào, tạo motel mặc định
          //console.log('No motels found, creating initial motel for user:', uid);
          //console.log('Creating initial motel data for displayName user:', user.displayName);
          const motelData = await initialMotel(uid);
          //console.log('Initial motel data:', motelData);
          const motelId = await addMotel(motelData);
          //console.log('Added motelId:', motelId);
          if (motelId) setMotelId(motelId);
        } else {
          setMotelId(firstMotel.motel_id);
        }
      } else {
        setMotelId(""); // Clear motelId on logout
      }
    });
    return () => unsubscribe();
  }, []);

  // Create dynamic theme based on current mode
  const paperTheme = createPaperTheme(isDarkMode);
  const navigationTheme = isDarkMode ? DarkTheme : DefaultTheme;

  return (
    <>
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"}
        backgroundColor={isDarkMode ? "#121212" : "#FFFFFF"}
      />
      <PaperProvider theme={paperTheme}>
        <NavigationContainer theme={navigationTheme}>
          <RootNavigator />
        </NavigationContainer>
      </PaperProvider>
    </>
  );
};

export default function App() {
  useEffect(() => {
    // configureGoogleSignIn(); // Temporarily commented out to fix runtime error
    console.log("App initialized");
  }, []);

  return (
    <DefaultPricesProvider>
      <ThemeProvider>
        <LanguageProvider>
          <MotelProvider>
            <AppContent />
          </MotelProvider>
        </LanguageProvider>
      </ThemeProvider>
    </DefaultPricesProvider>
  );
}
