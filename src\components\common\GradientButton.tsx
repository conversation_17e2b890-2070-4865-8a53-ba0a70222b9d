import { LinearGradient } from "expo-linear-gradient";
import {
  StyleProp,
  Text,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from "react-native";

const GradientButton = ({
  onPress,
  containerStyle,
  textStyle,
  text,
  renderIcon,
  linearGradientProps,
}: {
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  text: string;
  renderIcon?: () => React.ReactNode;
  linearGradientProps?: {
    start?: { x: number; y: number };
    end?: { x: number; y: number };
    locations?: number[];
    colors?: string[];
  };
}) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <LinearGradient
        colors={
          Array.isArray(linearGradientProps?.colors) &&
          linearGradientProps.colors.length >= 2
            ? (linearGradientProps.colors as [string, string, ...string[]])
            : ["#000000", "#FFFFFF"]
        }
        start={linearGradientProps?.start}
        end={linearGradientProps?.end}
        locations={
          Array.isArray(linearGradientProps?.locations) &&
          linearGradientProps.locations.length >= 2
            ? (linearGradientProps.locations as [number, number, ...number[]])
            : undefined
        }
        style={[
          {
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          },
          containerStyle,
        ]}
      >
        {renderIcon && renderIcon()}
        <Text style={textStyle}>{text}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default GradientButton;
