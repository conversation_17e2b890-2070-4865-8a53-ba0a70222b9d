import React, {
  useState,
  useEffect,
  useRef,
  use<PERSON><PERSON>back,
  useMemo,
} from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  ImageBackground,
  Alert,
  Platform,
  Share,
  ToastAndroid,
  TouchableOpacity,
} from "react-native";
import ViewShot from "react-native-view-shot";
import * as MediaLibrary from "expo-media-library";
import * as Sharing from "expo-sharing";
import {
  Surface,
  Text,
  Chip,
  Button,
  Divider,
  Avatar,
  ActivityIndicator,
  IconButton,
  Modal,
} from "react-native-paper";
import { useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { AppStackParamList, Room, Resident, HoaDon } from "../../types";
import { Colors, Typography, Spacing } from "../../theme";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { makePhoneCall, sendSMS } from "../../utils/phoneUtils";
import ResidentDialog from "../../components/ResidentDialog";
import ConfirmDeleteDialog from "../../components/ConfirmDeleteDialog";
import CollectPaymentDialog from "../../components/CollectPaymentDialog";
import {
  getBillsByRoomId,
  updateBillPaymentStatus,
} from "../../services/BillServices";
import {
  getRoomById,
  updateRoom,
  deleteRoom,
} from "../../services/roomService";
import {
  addTenant,
  updateTenant,
  deleteTenant,
  getTenantsByRoomId,
} from "../../services/TenantServices";
import { addLog } from "../../services/logService";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { useFocusEffect } from "@react-navigation/native";

type RoomDetailsRouteProp = RouteProp<AppStackParamList, "RoomDetails">;

export default function RoomDetails() {
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackParamList>>();
  const route = useRoute<RoomDetailsRouteProp>();
  const params = route.params;
  const roomId = params?.roomId || "room1";
  const { t } = useLanguage();
  const { isDarkMode } = useTheme();
  const billDetailsRef = useRef<any>(null);

  const [room, setRoom] = useState<Room | null>(null);
  const [residents, setResidents] = useState<Resident[]>([]);
  const [bills, setBills] = useState<HoaDon[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("info");
  const [selectedBill, setSelectedBill] = useState<HoaDon | null>(null);
  const [billDetailsVisible, setBillDetailsVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [hasMediaPermission, setHasMediaPermission] = useState(false);

  const [residentDialogVisible, setResidentDialogVisible] = useState(false);
  const [residentDialogMode, setResidentDialogMode] = useState<"add" | "edit">(
    "add"
  );
  const [selectedResident, setSelectedResident] = useState<Resident | null>(
    null
  );
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [residentToDelete, setResidentToDelete] = useState<Resident | null>(
    null
  );

  const [collectPaymentDialogVisible, setCollectPaymentDialogVisible] =
    useState(false);
  const [selectedBillForPayment, setSelectedBillForPayment] =
    useState<HoaDon | null>(null);
  const [deleteRoomDialogVisible, setDeleteRoomDialogVisible] = useState(false);

  useEffect(() => {
    (async () => {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      setHasMediaPermission(status === "granted");
    })();
  }, []);

  // Optimized data loading with better error handling and caching
  const loadRoomData = useCallback(async () => {
    setLoading(true);
    try {
      // Load data in parallel for better performance
      const [tenants, bills, roomData] = await Promise.all([
        getTenantsByRoomId(roomId),
        getBillsByRoomId(roomId),
        getRoomById(roomId),
      ]);

      setResidents(tenants);
      setBills(bills);

      if (roomData) {
        setRoom((prev) => ({
          ...prev,
          ...roomData,
        }));
      }
    } catch (error) {
      console.error("Error loading room data:", error);
      // Could add error state here for better UX
    } finally {
      setLoading(false);
    }
  }, [roomId]);

  // Luôn reload dữ liệu phòng khi màn hình được focus
  useFocusEffect(
    React.useCallback(() => {
      loadRoomData();
    }, [loadRoomData])
  );

  // Memoized currency formatter
  const formatCurrency = useCallback((amount: number) => {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " đ";
  }, []);

  const handlePhoneCall = async (phoneNumber: string, residentName: string) => {
    await makePhoneCall(phoneNumber, residentName, t);
  };

  const handleSendMessage = async (phoneNumber: string) => {
    await sendSMS(phoneNumber, "", t);
  };

  const handleAddResident = () => {
    setResidentDialogMode("add");
    setSelectedResident(null);
    setResidentDialogVisible(true);
  };

  const handleEditResident = (resident: Resident) => {
    setResidentDialogMode("edit");
    setSelectedResident(resident);
    setResidentDialogVisible(true);
  };

  const handleDeleteResident = (resident: Resident) => {
    setResidentToDelete(resident);
    setDeleteDialogVisible(true);

    updateRoom(roomId, { status: "available" });
    // Ở đây là 1 vì nó sắp bị xoá hết
    if (residents.length === 1 && room) {
      setRoom({ ...room, status: "available" });
    }
  };
  const handleSaveResident = async (residentData: Partial<Resident>) => {
    try {
      if (residentDialogMode === "add") {
        // Thêm tenant mới sử dụng TenantServices
        const tenantData = {
          name: residentData.name || "",
          phone: residentData.phone || "",
          identity_number: residentData.identity_number || "",
          is_main_tenant: residentData.is_main_tenant || false,
          room_id: roomId,
        };

        const result = await addTenant(tenantData);

        if (result.success) {
          // Ghi log thao tác thêm tenant
          if (room) {
            await addLog({
              action: "add",
              targetId: result.id || "",
              targetType: "tenant",
              motelId: room.motel_id,
              after: tenantData,
              description: `Thêm người thuê: ${tenantData.name}`,
            });
          }
          // Tải lại danh sách residents using getTenantsByRoomId
          updateRoom(roomId, { status: "occupied" });
          const updatedResidents = await getTenantsByRoomId(roomId);
          setResidents(updatedResidents);

          // Cập nhật trạng thái phòng nếu là người thuê đầu tiên
          if (residents.length === 0 && room) {
            setRoom({ ...room, status: "occupied" });
          }

          Alert.alert(t("common.success"), t("resident.addSuccess"));
        } else {
          Alert.alert(t("common.error"), t("resident.addError"));
        }
      } else {
        if (!selectedResident?.resident_id) {
          Alert.alert(t("common.error"), t("resident.updateError"));
          return;
        }
        // Lấy dữ liệu trước khi update
        const before = residents.find(
          (r) => r.resident_id === selectedResident.resident_id
        );
        const success = await updateTenant(selectedResident.resident_id, {
          name: residentData.name || "",
          phone: residentData.phone || "",
          identity_number: residentData.identity_number || "",
          is_main_tenant: residentData.is_main_tenant || false,
        });
        if (success) {
          // Ghi log thao tác cập nhật tenant
          if (room && before) {
            await addLog({
              action: "edit",
              targetId: selectedResident.resident_id,
              targetType: "tenant",
              motelId: room.motel_id,
              before,
              after: residentData,
              description: `Cập nhật người thuê: ${
                residentData.name || before.name
              }`,
            });
          }
          const updatedResidents = await getTenantsByRoomId(roomId);
          setResidents(updatedResidents);
          Alert.alert(t("common.success"), t("resident.updateSuccess"));
        } else {
          Alert.alert(t("common.error"), t("resident.updateError"));
        }
      }
    } catch (error) {
      console.error(error);
      Alert.alert(t("common.error"), t("common.unknownError"));
    }
  };

  const handleConfirmDeleteResident = async () => {
    if (residentToDelete) {
      // Lấy dữ liệu trước khi xóa
      const before = residents.find(
        (r) => r.resident_id === residentToDelete.resident_id
      );
      const success = await deleteTenant(residentToDelete.resident_id);
      if (success) {
        // Ghi log thao tác xóa tenant
        if (room && before) {
          await addLog({
            action: "delete",
            targetId: residentToDelete.resident_id,
            targetType: "tenant",
            motelId: room.motel_id,
            before,
            description: `Xóa người thuê: ${before.name}`,
          });
        }
        const updatedResidents = await getTenantsByRoomId(roomId);
        setResidents(updatedResidents);
        Alert.alert(t("common.success"), t("resident.deleteSuccess"));
      } else {
        Alert.alert(t("common.error"), t("resident.deleteError"));
      }
      setResidentToDelete(null);
    }
  };

  const handleDeleteRoom = () => {
    setDeleteRoomDialogVisible(true);
  };

  const handleConfirmDeleteRoom = async () => {
    if (!room) return;

    try {
      // Ghi log trước khi xóa
      await addLog({
        action: "delete",
        targetId: roomId,
        targetType: "room",
        motelId: room.motel_id,
        before: room,
        description: `Xóa phòng: ${room.room_number}`,
      });

      const success = await deleteRoom(roomId);
      if (success) {
        Alert.alert(t("common.success"), t("room.deleteSuccess"), [
          {
            text: t("common.ok"),
            onPress: () => navigation.goBack(),
          },
        ]);
      } else {
        Alert.alert(t("common.error"), t("room.deleteError"));
      }
    } catch (error) {
      console.error("Error deleting room:", error);
      Alert.alert(t("common.error"), t("room.deleteError"));
    } finally {
      setDeleteRoomDialogVisible(false);
    }
  };

  const handleEditRoom = () => {
    if (!room) return;

    (navigation as any).navigate("AddRoom", {
      mode: "edit",
      roomData: room,
    });
  };

  // Hàm thu tiền hóa đơn
  const handleCollectPayment = async (billId: string) => {
    setCollectPaymentDialogVisible(true);
    try {
      // Tính fix nhưng thôi để a trí sửa chỗ này vậy
      const ok = await updateBillPaymentStatus(billId, "paid");
      if (ok) {
        ToastAndroid.show(t("payment.updateStatusSuccess"), ToastAndroid.SHORT);
        // Có thể refresh lại danh sách hóa đơn nếu cần
      } else {
        ToastAndroid.show(t("payment.updateStatusFailed"), ToastAndroid.SHORT);
      }
    } catch (e) {
      ToastAndroid.show(t("payment.collectError"), ToastAndroid.SHORT);
    }
  };

  const handlePaymentCollected = (paymentData: any) => {
    if (selectedBillForPayment) {
      setBills(
        bills.map((bill) =>
          bill.hoadon_id === selectedBillForPayment.hoadon_id
            ? { ...bill, payment_status: "paid" as const }
            : bill
        )
      );

      Alert.alert(
        t("common.success"),
        t("payment.paymentRecorded", {
          amount: paymentData.amount,
          method: t(`payment.${paymentData.paymentMethod}`),
        })
      );
    }
  };

  // Memoized bill statistics calculation for better performance
  const billStats = useMemo(() => {
    const totalBills = bills.length;
    const paidBills = bills.filter(
      (bill) => bill.payment_status === "paid"
    ).length;
    const pendingBills = bills.filter(
      (bill) => bill.payment_status === "pending"
    ).length;
    const overdueBills = bills.filter(
      (bill) => bill.payment_status === "overdue"
    ).length;

    const totalAmount = bills.reduce((sum, bill) => sum + bill.tong_tien, 0);
    const paidAmount = bills
      .filter((bill) => bill.payment_status === "paid")
      .reduce((sum, bill) => sum + bill.tong_tien, 0);
    const pendingAmount = bills
      .filter((bill) => bill.payment_status === "pending")
      .reduce((sum, bill) => sum + bill.tong_tien, 0);
    const overdueAmount = bills
      .filter((bill) => bill.payment_status === "overdue")
      .reduce((sum, bill) => sum + bill.tong_tien, 0);

    return {
      totalBills,
      paidBills,
      pendingBills,
      overdueBills,
      totalAmount,
      paidAmount,
      pendingAmount,
      overdueAmount,
    };
  }, [bills]);

  // Memoized filtered bills for better performance
  const filteredBills = useMemo(() => {
    return filterStatus
      ? bills.filter((bill) => bill.payment_status === filterStatus)
      : bills;
  }, [bills, filterStatus]);

  if (loading || !room) {
    return (
      <View
        style={[
          styles.loadingContainer,
          isDarkMode && styles.darkLoadingContainer,
        ]}
      >
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text
          style={[styles.loadingText, isDarkMode && styles.darkLoadingText]}
        >
          {t("room.loadingDetails")}
        </Text>
      </View>
    );
  }

  const renderInfoTab = () => (
    <View style={styles.tabContent}>
      <Surface
        style={[styles.infoSection, isDarkMode && styles.darkInfoSection]}
        elevation={1}
      >
        <View style={styles.sectionHeaderWithButton}>
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, isDarkMode && styles.darkSectionTitle]}
          >
            {t("room.basicInfo")}
          </Text>
        </View>
        <Divider style={[styles.divider, isDarkMode && styles.darkDivider]} />
        <View style={styles.infoGrid}>
          <View style={styles.infoItem}>
            <Text
              style={[styles.infoLabel, isDarkMode && styles.darkInfoLabel]}
            >
              {t("room.area")}
            </Text>
            <Text
              style={[styles.infoValue, isDarkMode && styles.darkInfoValue]}
            >
              {room.dienTich} m²
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text
              style={[styles.infoLabel, isDarkMode && styles.darkInfoLabel]}
            >
              {t("room.rentalPrice")}
            </Text>
            <Text
              style={[styles.infoValue, isDarkMode && styles.darkInfoValue]}
            >
              {formatCurrency(room.gia)}/{t("room.month")}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text
              style={[styles.infoLabel, isDarkMode && styles.darkInfoLabel]}
            >
              {t("room.deposit")}
            </Text>
            <Text
              style={[styles.infoValue, isDarkMode && styles.darkInfoValue]}
            >
              {formatCurrency(room.tienCoc)}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text
              style={[styles.infoLabel, isDarkMode && styles.darkInfoLabel]}
            >
              {t("room.notes")}
            </Text>
            <Text
              style={[styles.infoValue, isDarkMode && styles.darkInfoValue]}
            >
              {room.ghiChu || t("room.noNotes")}
            </Text>
          </View>
        </View>
      </Surface>

      <Surface
        style={[styles.infoSection, isDarkMode && styles.darkInfoSection]}
        elevation={1}
      >
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, isDarkMode && styles.darkSectionTitle]}
        >
          {t("room.roomType")}
        </Text>
        <Divider style={[styles.divider, isDarkMode && styles.darkDivider]} />
        <View style={styles.chipContainer}>
          {room.loaiPhong.map((type: string, index: number) => (
            <Chip
              key={index}
              style={[styles.chip, isDarkMode && styles.darkChip]}
              icon="home-variant"
              textStyle={{
                color: isDarkMode
                  ? Colors.DARK.TEXT_PRIMARY
                  : Colors.TEXT_PRIMARY,
              }}
            >
              {t(type)}
            </Chip>
          ))}
        </View>
      </Surface>

      <Surface
        style={[styles.infoSection, isDarkMode && styles.darkInfoSection]}
        elevation={1}
      >
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, isDarkMode && styles.darkSectionTitle]}
        >
          {t("room.amenities")}
        </Text>
        <Divider style={[styles.divider, isDarkMode && styles.darkDivider]} />
        <View style={styles.chipContainer}>
          {room.tienNghi.map((amenity: string, index: number) => (
            <Chip
              key={index}
              style={[styles.chip, isDarkMode && styles.darkChip]}
              icon="check-circle"
              textStyle={{
                color: isDarkMode
                  ? Colors.DARK.TEXT_PRIMARY
                  : Colors.TEXT_PRIMARY,
              }}
            >
              {t(amenity)}
            </Chip>
          ))}
        </View>
      </Surface>
      <Button
        mode="contained"
        icon="pencil"
        onPress={handleEditRoom}
        style={styles.footerEditButton}
        buttonColor={Colors.PRIMARY}
      >
        {t("common.edit")}
      </Button>
    </View>
  );

  const renderResidentsTab = () => (
    <View style={styles.tabContent}>
      {/* Add Resident Button */}
      <Button
        mode="contained"
        icon="account-plus"
        onPress={handleAddResident}
        style={styles.addButton}
        buttonColor="#70C4D7"
        textColor="white"
      >
        {t("resident.addResident")}
      </Button>

      {residents.length === 0 ? (
        <Surface
          style={[
            styles.emptyContainer,
            isDarkMode && styles.darkEmptyContainer,
          ]}
          elevation={1}
        >
          <MaterialCommunityIcons
            name="account-off"
            size={48}
            color={isDarkMode ? Colors.DARK.GRAY_DARK : Colors.GRAY_DARK}
          />
          <Text style={[styles.emptyText, isDarkMode && styles.darkEmptyText]}>
            {t("room.noTenants")}
          </Text>
        </Surface>
      ) : (
        residents.map((resident) => (
          <Surface
            key={resident.resident_id}
            style={[styles.residentCard, isDarkMode && styles.darkResidentCard]}
            elevation={1}
          >
            <View style={styles.residentHeader}>
              <Avatar.Text
                size={50}
                label={resident.name
                  .split(" ")
                  .map((n: string) => n[0])
                  .join("")}
                style={{ backgroundColor: Colors.PRIMARY }}
              />
              <View style={styles.residentInfo}>
                <Text
                  variant="titleMedium"
                  style={isDarkMode ? styles.darkInfoValue : undefined}
                >
                  {resident.name}
                </Text>
                <Text variant="bodyMedium" style={{ color: Colors.PRIMARY }}>
                  {resident.is_main_tenant
                    ? t("room.mainTenant")
                    : t("room.resident")}
                </Text>
                <Text
                  variant="bodyMedium"
                  style={isDarkMode ? styles.darkInfoLabel : undefined}
                >
                  {resident.phone}
                </Text>
              </View>
              <View style={styles.residentActions}>
                <IconButton
                  icon="pencil"
                  size={20}
                  iconColor="#70C4D7"
                  onPress={() => handleEditResident(resident)}
                />
                <IconButton
                  icon="delete"
                  size={20}
                  iconColor="#FF6B6B"
                  onPress={() => handleDeleteResident(resident)}
                />
              </View>
            </View>
            <Divider
              style={[styles.divider, isDarkMode && styles.darkDivider]}
            />
            <View style={styles.residentActions}>
              <Button
                mode="contained-tonal"
                icon="phone"
                onPress={() => handlePhoneCall(resident.phone, resident.name)}
                style={styles.footerActionButton}
                buttonColor={
                  isDarkMode ? Colors.DARK.GRAY_LIGHT : Colors.GRAY_LIGHT
                }
                textColor={
                  isDarkMode ? Colors.DARK.TEXT_PRIMARY : Colors.TEXT_PRIMARY
                }
              >
                {t("room.call")}
              </Button>
              <Button
                mode="contained-tonal"
                icon="message"
                onPress={() => handleSendMessage(resident.phone)}
                style={styles.footerActionButton}
                buttonColor={
                  isDarkMode ? Colors.DARK.GRAY_LIGHT : Colors.GRAY_LIGHT
                }
                textColor={
                  isDarkMode ? Colors.DARK.TEXT_PRIMARY : Colors.TEXT_PRIMARY
                }
              >
                {t("room.message")}
              </Button>
            </View>
          </Surface>
        ))
      )}
    </View>
  );

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return Colors.PAYMENT_PAID;
      case "pending":
        return Colors.PAYMENT_PENDING;
      case "overdue":
        return Colors.PAYMENT_OVERDUE;
      case "partial":
        return Colors.PAYMENT_PARTIAL;
      default:
        return Colors.GRAY_MEDIUM;
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return "check-circle";
      case "pending":
        return "clock-outline";
      case "overdue":
        return "alert-circle";
      case "partial":
        return "circle-half-full";
      default:
        return "help-circle";
    }
  };

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case "paid":
        return t("payment.paid");
      case "pending":
        return t("payment.pending");
      case "overdue":
        return t("payment.overdue");
      default:
        return t("payment.unknown");
    }
  };

  const handleBillPress = (bill: HoaDon) => {
    setSelectedBill(bill);
    setBillDetailsVisible(true);
  };

  const handleAddBill = () => {
    navigation.navigate("BillScreen", {
      roomId: room.room_id,
      isEdit: false,
    });
  };

  const handleEditBill = (bill: HoaDon) => {
    navigation.navigate("BillScreen", {
      billId: bill.hoadon_id,
      roomId: room.room_id,
      isEdit: true,
    });
  };

  const handleSaveBill = async () => {
    if (!billDetailsRef.current) return;

    try {
      setIsSaving(true);

      if (!hasMediaPermission) {
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            t("common.error"),
            t("payment.storagePermissionRequired") ||
              "Storage permission is required"
          );
          return;
        }
        setHasMediaPermission(true);
      }

      const uri = await billDetailsRef.current.capture({
        format: "png",
        quality: 1,
        result: "file",
      });

      const asset = await MediaLibrary.createAssetAsync(uri);
      const album = await MediaLibrary.getAlbumAsync("Motel Management");

      if (album) {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      } else {
        await MediaLibrary.createAlbumAsync("Motel Management", asset, false);
      }

      if (Platform.OS === "android") {
        ToastAndroid.show(t("payment.billSavedToGallery"), ToastAndroid.LONG);
      } else {
        Alert.alert(t("payment.saveSuccess"), t("payment.billSavedToGallery"));
      }
    } catch (error) {
      console.error("Error saving bill:", error);
      Alert.alert(t("payment.saveError"), t("payment.saveErrorMessage"));
    } finally {
      setIsSaving(false);
    }
  };

  const handleShareBill = async () => {
    if (!billDetailsRef.current) return;

    try {
      setIsSharing(true);

      const uri = await billDetailsRef.current.capture({
        format: "png",
        quality: 1,
        result: "file",
      });

      const isAvailable = await Sharing.isAvailableAsync();

      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: "image/png",
          dialogTitle: `${t("payment.billFor")} ${room?.room_number} - ${
            selectedBill?.thang_hoa_don
          }`,
        });
      } else {
        await Share.share({
          url: uri,
          title: `${t("payment.billFor")} ${room?.room_number} - ${
            selectedBill?.thang_hoa_don
          }`,
        });
      }
    } catch (error) {
      console.error("Error sharing bill:", error);
      Alert.alert(t("payment.shareError"), t("payment.shareErrorMessage"));
    } finally {
      setIsSharing(false);
    }
  };

  const renderReceiptModal = () => {
    if (!selectedBill) return null;

    return (
      <Surface
        style={[styles.modalContent, isDarkMode && styles.darkModalContent]}
      >
        <View style={styles.modalHeader}>
          <Text
            variant="titleLarge"
            style={[styles.modalTitle, isDarkMode && styles.darkModalTitle]}
          >
            {t("payment.receipt")}
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={() => setBillDetailsVisible(false)}
            iconColor={
              isDarkMode ? Colors.DARK.TEXT_PRIMARY : Colors.TEXT_PRIMARY
            }
          />
        </View>

        <ScrollView style={styles.modalScrollContent}>
          <ViewShot
            ref={billDetailsRef}
            options={{ format: "png", quality: 0.9 }}
            style={[
              styles.billDetailsContent,
              {
                backgroundColor: isDarkMode
                  ? Colors.DARK.CARD_BACKGROUND
                  : Colors.WHITE,
              },
            ]}
          >
            <View style={styles.billHeader}>
              <View>
                <Text
                  variant="titleMedium"
                  style={[styles.billMonth, isDarkMode && styles.darkBillMonth]}
                >
                  {selectedBill.thang_hoa_don}
                </Text>
                <Text
                  variant="bodyMedium"
                  style={[styles.billDate, isDarkMode && styles.darkBillDate]}
                >
                  {t("payment.createdOn")}:{" "}
                  {selectedBill.created_at.toLocaleDateString()}
                </Text>
              </View>
              <Chip
                style={{
                  backgroundColor: getPaymentStatusColor(
                    selectedBill.payment_status
                  ),
                  paddingHorizontal: 8,
                  height: 36,
                }}
                textStyle={{ color: Colors.WHITE, fontWeight: "bold" }}
                icon={() => (
                  <MaterialCommunityIcons
                    name={getPaymentStatusIcon(selectedBill.payment_status)}
                    size={18}
                    color={Colors.WHITE}
                    style={{ marginRight: 4 }}
                  />
                )}
              >
                {getPaymentStatusText(selectedBill.payment_status)}
              </Chip>
            </View>

            <Surface
              style={[styles.billSection, isDarkMode && styles.darkBillSection]}
              elevation={2}
            >
              <Text
                variant="titleMedium"
                style={[
                  styles.sectionTitle,
                  isDarkMode && styles.darkSectionTitle,
                ]}
              >
                {t("payment.summary")}
              </Text>
              <Divider
                style={[styles.divider, isDarkMode && styles.darkDivider]}
              />

              <View style={styles.billRow}>
                <Text
                  style={[styles.billLabel, isDarkMode && styles.darkBillLabel]}
                >
                  {t("room.roomFee")}
                </Text>
                <Text
                  style={[styles.billValue, isDarkMode && styles.darkBillValue]}
                >
                  {formatCurrency(selectedBill.tienphong)}
                </Text>
              </View>

              <View style={styles.billRow}>
                <Text
                  style={[styles.billLabel, isDarkMode && styles.darkBillLabel]}
                >
                  {t("payment.electricity")}
                </Text>
                <Text
                  style={[styles.billValue, isDarkMode && styles.darkBillValue]}
                >
                  {formatCurrency(selectedBill.tiendien)}
                </Text>
              </View>

              <View style={styles.billRow}>
                <Text
                  style={[styles.billLabel, isDarkMode && styles.darkBillLabel]}
                >
                  {t("payment.water")}
                </Text>
                <Text
                  style={[styles.billValue, isDarkMode && styles.darkBillValue]}
                >
                  {formatCurrency(selectedBill.tiennuoc)}
                </Text>
              </View>

              {selectedBill.additional_fees.map((fee, index) => (
                <View key={index} style={styles.billRow}>
                  <Text
                    style={[
                      styles.billLabel,
                      isDarkMode && styles.darkBillLabel,
                    ]}
                  >
                    {fee.name}
                  </Text>
                  <Text
                    style={[
                      styles.billValue,
                      isDarkMode && styles.darkBillValue,
                    ]}
                  >
                    {formatCurrency(fee.amount)}
                  </Text>
                </View>
              ))}

              <Divider
                style={[
                  styles.divider,
                  isDarkMode && styles.darkDivider,
                  styles.totalDivider,
                ]}
              />

              <View style={styles.billRow}>
                <Text
                  style={[
                    styles.billLabel,
                    styles.totalLabel,
                    isDarkMode && styles.darkTotalLabel,
                  ]}
                >
                  {t("room.total")}
                </Text>
                <Text
                  style={[
                    styles.billValue,
                    styles.totalValue,
                    isDarkMode && styles.darkTotalValue,
                  ]}
                >
                  {formatCurrency(selectedBill.tong_tien)}
                </Text>
              </View>
            </Surface>

            <Surface
              style={[styles.billSection, isDarkMode && styles.darkBillSection]}
              elevation={2}
            >
              <Text
                variant="titleMedium"
                style={[
                  styles.sectionTitle,
                  isDarkMode && styles.darkSectionTitle,
                ]}
              >
                {t("payment.paymentDetails")}
              </Text>
              <Divider
                style={[styles.divider, isDarkMode && styles.darkDivider]}
              />

              <View style={styles.billRow}>
                <Text
                  style={[styles.billLabel, isDarkMode && styles.darkBillLabel]}
                >
                  {t("payment.dueDate")}
                </Text>
                <Text
                  style={[styles.billValue, isDarkMode && styles.darkBillValue]}
                >
                  {selectedBill.due_date.toLocaleDateString()}
                </Text>
              </View>

              <View style={styles.billRow}>
                <Text
                  style={[styles.billLabel, isDarkMode && styles.darkBillLabel]}
                >
                  {t("payment.status")}
                </Text>
                <Text
                  style={[
                    styles.billValue,
                    {
                      color: getPaymentStatusColor(selectedBill.payment_status),
                      fontWeight: "bold",
                    },
                  ]}
                >
                  {getPaymentStatusText(selectedBill.payment_status)}
                </Text>
              </View>

              {selectedBill.payment_status === "overdue" && (
                <View style={styles.billRow}>
                  <Text
                    style={[
                      styles.billLabel,
                      isDarkMode && styles.darkBillLabel,
                    ]}
                  >
                    {t("payment.overdueDays")}
                  </Text>
                  <Text
                    style={[
                      styles.billValue,
                      {
                        color: Colors.DANGER,
                        fontWeight: "bold",
                      },
                    ]}
                  >
                    {selectedBill.overdue_days} {t("payment.days")}
                  </Text>
                </View>
              )}
            </Surface>
          </ViewShot>

          <View style={styles.actionButtonsContainer}>
            <Button
              mode="contained-tonal"
              icon="content-save"
              onPress={handleSaveBill}
              style={styles.actionButton}
              buttonColor={
                isDarkMode ? Colors.DARK.GRAY_LIGHT : Colors.GRAY_LIGHT
              }
              textColor={
                isDarkMode ? Colors.DARK.TEXT_PRIMARY : Colors.TEXT_PRIMARY
              }
              loading={isSaving}
            >
              {t("payment.save")}
            </Button>

            <Button
              mode="contained-tonal"
              icon="share-variant"
              onPress={handleShareBill}
              style={styles.actionButton}
              buttonColor={Colors.PRIMARY}
              textColor={Colors.WHITE}
              loading={isSharing}
            >
              {t("payment.share")}
            </Button>

            {/* <Button
              mode="contained-tonal"
              icon="email-send"
              onPress={() => {
                Alert.alert(
                  t("payment.sendReminder"),
                  t("payment.reminderSent")
                );
              }}
              style={styles.actionButton}
              buttonColor={
                isDarkMode ? Colors.DARK.GRAY_LIGHT : Colors.GRAY_LIGHT
              }
              textColor={
                isDarkMode ? Colors.DARK.TEXT_PRIMARY : Colors.TEXT_PRIMARY
              }
            >
              {t("payment.sendReminder")}
            </Button> */}
          </View>
        </ScrollView>
      </Surface>
    );
  };

  const renderInvoiceModal = () => {
    if (!selectedBill) return null;

    return (
      <Surface style={[styles.invoiceModalContent]}>
        {/* <View style={styles.invoiceModalHeader}>
          <Text variant="titleLarge" style={styles.invoiceModalTitle}>
            {t("payment.invoice")}
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={() => setBillDetailsVisible(false)}
            iconColor="#FFFFFF"
          />
        </View> */}

        <ScrollView style={styles.modalScrollContent}>
          <IconButton
            icon="close"
            size={24}
            onPress={() => setBillDetailsVisible(false)}
            iconColor="#000000"
            style={{
              position: "absolute",
              top: 1,
              right: 1,
              zIndex: 1,
            }}
          />
          <ViewShot
            ref={billDetailsRef}
            options={{ format: "png", quality: 0.9 }}
            style={styles.invoiceBillDetailsContent}
          >
            <View style={styles.invoiceBillHeader}>
              <View>
                <Text variant="titleLarge" style={styles.invoiceBillMonth}>
                  {t("payment.invoice")} - {selectedBill.thang_hoa_don}
                </Text>
                <Text variant="bodyMedium" style={styles.invoiceBillDate}>
                  {t("payment.paidOn")}:{" "}
                  {selectedBill.created_at.toLocaleDateString()}
                </Text>
              </View>
            </View>

            <View style={styles.invoiceBillSection}>
              <Text style={styles.invoiceSectionTitle}>
                {t("payment.summary")}
              </Text>

              <View style={styles.invoiceDivider} />

              <View style={styles.invoiceBillRow}>
                <Text style={styles.invoiceBillLabel}>{t("room.roomFee")}</Text>
                <Text style={styles.invoiceBillValue}>
                  {formatCurrency(selectedBill.tienphong)}
                </Text>
              </View>

              <View style={styles.invoiceBillRow}>
                <Text style={styles.invoiceBillLabel}>
                  {t("payment.electricity")}
                </Text>
                <Text style={styles.invoiceBillValue}>
                  {formatCurrency(selectedBill.tiendien)}
                </Text>
              </View>

              <View style={styles.invoiceBillRow}>
                <Text style={styles.invoiceBillLabel}>
                  {t("payment.water")}
                </Text>
                <Text style={styles.invoiceBillValue}>
                  {formatCurrency(selectedBill.tiennuoc)}
                </Text>
              </View>

              {selectedBill.additional_fees.map((fee, index) => (
                <View key={index} style={styles.invoiceBillRow}>
                  <Text style={styles.invoiceBillLabel}>{fee.name}</Text>
                  <Text style={styles.invoiceBillValue}>
                    {formatCurrency(fee.amount)}
                  </Text>
                </View>
              ))}

              <View style={styles.invoiceDivider} />

              <View
                style={[
                  styles.invoiceBillRow,
                  {
                    borderBottomWidth: 0,
                    paddingTop: 15,
                    backgroundColor: "#000000",
                    marginHorizontal: -20,
                    paddingHorizontal: 20,
                    marginBottom: -20,
                  },
                ]}
              >
                <Text style={[styles.invoiceTotalLabel, { color: "#FFFFFF" }]}>
                  {t("room.total")}
                </Text>
                <Text style={[styles.invoiceTotalValue, { color: "#FFFFFF" }]}>
                  {formatCurrency(selectedBill.tong_tien)}
                </Text>
              </View>
            </View>

            <View style={styles.invoiceBillSection}>
              <Text style={styles.invoiceSectionTitle}>
                {t("payment.paymentDetails")}
              </Text>

              <View style={styles.invoiceDivider} />

              <View style={styles.invoiceBillRow}>
                <Text style={styles.invoiceBillLabel}>
                  {t("payment.paymentDate")}
                </Text>
                <Text style={styles.invoiceBillValue}>
                  {selectedBill.created_at.toLocaleDateString()}
                </Text>
              </View>

              <View style={styles.invoiceBillRow}>
                <Text style={styles.invoiceBillLabel}>
                  {t("payment.status")}
                </Text>
                <Text style={styles.invoiceStatusValue}>
                  {t("payment.paid")}
                </Text>
              </View>
            </View>
          </ViewShot>

          <View style={styles.invoiceActionButtonsContainer}>
            <Button
              mode="outlined"
              icon="content-save"
              onPress={handleSaveBill}
              style={styles.invoiceActionButton}
              buttonColor="#FFFFFF"
              textColor="#000000"
              loading={isSaving}
            >
              {t("payment.save")}
            </Button>

            <Button
              mode="contained"
              icon="share-variant"
              onPress={handleShareBill}
              style={styles.invoiceActionButton}
              buttonColor="#000000"
              textColor="#FFFFFF"
              loading={isSharing}
            >
              {t("payment.share")}
            </Button>
          </View>
        </ScrollView>
      </Surface>
    );
  };

  const renderPaymentsTab = () => {
    const stats = billStats;

    return (
      <View style={styles.tabContent}>
        {/* Luôn hiển thị button Thêm hóa đơn */}
        <View style={styles.addBillContainer}>
          <Button
            mode="contained"
            icon="plus"
            onPress={handleAddBill}
            style={styles.addBillButton}
            buttonColor={Colors.PRIMARY}
          >
            {t("payment.addBill")}
          </Button>
        </View>
        {bills.length === 0 ? (
          <Surface
            style={[
              styles.emptyContainer,
              isDarkMode && styles.darkEmptyContainer,
            ]}
            elevation={1}
          >
            <MaterialCommunityIcons
              name="file-document"
              size={48}
              color={isDarkMode ? Colors.DARK.GRAY_DARK : Colors.GRAY_DARK}
            />
            <Text
              style={[styles.emptyText, isDarkMode && styles.darkEmptyText]}
            >
              {t("room.noBills")}
            </Text>
          </Surface>
        ) : (
          <>
            <Surface
              style={[
                styles.summaryContainer,
                isDarkMode && styles.darkSummaryContainer,
              ]}
              elevation={2}
            >
              <Text
                variant="titleMedium"
                style={[
                  styles.summaryTitle,
                  isDarkMode && styles.darkSummaryTitle,
                ]}
              >
                {t("payment.summary")}
              </Text>

              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Text
                    style={[
                      styles.statValue,
                      isDarkMode && styles.darkStatValue,
                    ]}
                  >
                    {formatCurrency(stats.totalAmount)}
                  </Text>
                  <Text
                    style={[
                      styles.statLabel,
                      isDarkMode && styles.darkStatLabel,
                    ]}
                  >
                    {t("payment.totalBilled")}
                  </Text>
                </View>

                <View style={styles.statItem}>
                  <Text
                    style={[
                      styles.statValue,
                      { color: Colors.PAYMENT_PAID },
                      isDarkMode && { color: Colors.PAYMENT_PAID },
                    ]}
                  >
                    {formatCurrency(stats.paidAmount)}
                  </Text>
                  <Text
                    style={[
                      styles.statLabel,
                      isDarkMode && styles.darkStatLabel,
                    ]}
                  >
                    {t("payment.paid")}
                  </Text>
                </View>

                <View style={styles.statItem}>
                  <Text
                    style={[
                      styles.statValue,
                      {
                        color:
                          stats.pendingAmount > 0
                            ? Colors.PAYMENT_PENDING
                            : stats.overdueAmount > 0
                            ? Colors.PAYMENT_OVERDUE
                            : Colors.GRAY_MEDIUM,
                      },
                      isDarkMode && {
                        color:
                          stats.pendingAmount > 0
                            ? Colors.PAYMENT_PENDING
                            : stats.overdueAmount > 0
                            ? Colors.PAYMENT_OVERDUE
                            : Colors.DARK.GRAY_DARK,
                      },
                    ]}
                  >
                    {formatCurrency(stats.pendingAmount + stats.overdueAmount)}
                  </Text>
                  <Text
                    style={[
                      styles.statLabel,
                      isDarkMode && styles.darkStatLabel,
                    ]}
                  >
                    {t("payment.outstanding")}
                  </Text>
                </View>
              </View>

              <View style={styles.progressBarContainer}>
                <View style={styles.progressBar}>
                  {stats.totalAmount > 0 && (
                    <>
                      <View
                        style={[
                          styles.progressBarSegment,
                          {
                            backgroundColor: Colors.PAYMENT_PAID,
                            width: `${
                              (stats.paidAmount / stats.totalAmount) * 100
                            }%`,
                          },
                        ]}
                      />
                      <View
                        style={[
                          styles.progressBarSegment,
                          {
                            backgroundColor: Colors.PAYMENT_PENDING,
                            width: `${
                              (stats.pendingAmount / stats.totalAmount) * 100
                            }%`,
                          },
                        ]}
                      />
                      <View
                        style={[
                          styles.progressBarSegment,
                          {
                            backgroundColor: Colors.PAYMENT_OVERDUE,
                            width: `${
                              (stats.overdueAmount / stats.totalAmount) * 100
                            }%`,
                          },
                        ]}
                      />
                    </>
                  )}
                </View>
              </View>
            </Surface>

            <View style={styles.filterContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <Chip
                  selected={filterStatus === null}
                  onPress={() => setFilterStatus(null)}
                  style={[
                    styles.filterChip,
                    isDarkMode && styles.darkFilterChip,
                    filterStatus === null && {
                      backgroundColor: Colors.PRIMARY,
                      borderWidth: 0,
                    },
                  ]}
                  textStyle={{
                    color:
                      filterStatus === null
                        ? Colors.WHITE
                        : isDarkMode
                        ? Colors.DARK.TEXT_PRIMARY
                        : Colors.TEXT_PRIMARY,
                    fontWeight: filterStatus === null ? "bold" : "normal",
                  }}
                  icon={filterStatus === null ? "check" : undefined}
                >
                  {t("payment.all")} ({bills.length})
                </Chip>

                <Chip
                  selected={filterStatus === "paid"}
                  onPress={() => setFilterStatus("paid")}
                  style={[
                    styles.filterChip,
                    isDarkMode && styles.darkFilterChip,
                    filterStatus === "paid" && {
                      backgroundColor: Colors.PAYMENT_PAID,
                      borderWidth: 0,
                    },
                  ]}
                  textStyle={{
                    color:
                      filterStatus === "paid"
                        ? Colors.WHITE
                        : isDarkMode
                        ? Colors.DARK.TEXT_PRIMARY
                        : Colors.TEXT_PRIMARY,
                    fontWeight: filterStatus === "paid" ? "bold" : "normal",
                  }}
                  icon={filterStatus === "paid" ? "check-circle" : undefined}
                >
                  {t("payment.paid")} ({stats.paidBills})
                </Chip>

                <Chip
                  selected={filterStatus === "pending"}
                  onPress={() => setFilterStatus("pending")}
                  style={[
                    styles.filterChip,
                    isDarkMode && styles.darkFilterChip,
                    filterStatus === "pending" && {
                      backgroundColor: Colors.PAYMENT_PENDING,
                      borderWidth: 0,
                    },
                  ]}
                  textStyle={{
                    color:
                      filterStatus === "pending"
                        ? Colors.WHITE
                        : isDarkMode
                        ? Colors.DARK.TEXT_PRIMARY
                        : Colors.TEXT_PRIMARY,
                    fontWeight: filterStatus === "pending" ? "bold" : "normal",
                  }}
                  icon={
                    filterStatus === "pending" ? "clock-outline" : undefined
                  }
                >
                  {t("payment.pending")} ({stats.pendingBills})
                </Chip>

                <Chip
                  selected={filterStatus === "overdue"}
                  onPress={() => setFilterStatus("overdue")}
                  style={[
                    styles.filterChip,
                    isDarkMode && styles.darkFilterChip,
                    filterStatus === "overdue" && {
                      backgroundColor: Colors.PAYMENT_OVERDUE,
                      borderWidth: 0,
                    },
                  ]}
                  textStyle={{
                    color:
                      filterStatus === "overdue"
                        ? Colors.WHITE
                        : isDarkMode
                        ? Colors.DARK.TEXT_PRIMARY
                        : Colors.TEXT_PRIMARY,
                    fontWeight: filterStatus === "overdue" ? "bold" : "normal",
                  }}
                  icon={filterStatus === "overdue" ? "alert-circle" : undefined}
                >
                  {t("payment.overdue")} ({stats.overdueBills})
                </Chip>
              </ScrollView>
            </View>

            <View style={styles.billsContainer}>
              {filteredBills.map((bill) => (
                <Surface
                  key={bill.hoadon_id}
                  style={[styles.billCard, isDarkMode && styles.darkBillCard]}
                  elevation={2}
                >
                  <TouchableOpacity
                    style={styles.billCardContent}
                    onPress={() => handleBillPress(bill)}
                    activeOpacity={0.7}
                  >
                    {/* Bill Header */}
                    <View style={styles.billCardHeader}>
                      <View style={styles.billCardLeft}>
                        <View style={styles.billIconContainer}>
                          <MaterialCommunityIcons
                            name="receipt"
                            size={24}
                            color={Colors.PRIMARY}
                          />
                        </View>
                        <View style={styles.billCardInfo}>
                          <Text
                            style={[
                              styles.billCardMonth,
                              isDarkMode && styles.darkBillCardMonth,
                            ]}
                          >
                            {bill.thang_hoa_don}
                          </Text>
                          <Text
                            style={[
                              styles.billCardDate,
                              isDarkMode && styles.darkBillCardDate,
                            ]}
                          >
                            {bill.created_at.toLocaleDateString()}
                          </Text>
                        </View>
                      </View>

                      <View style={styles.billCardRight}>
                        <Text
                          style={[
                            styles.billCardAmount,
                            isDarkMode && styles.darkBillCardAmount,
                          ]}
                        >
                          {formatCurrency(bill.tong_tien)}
                        </Text>
                        <Chip
                          style={[
                            styles.billStatusChip,
                            {
                              backgroundColor: getPaymentStatusColor(
                                bill.payment_status
                              ),
                            },
                          ]}
                          textStyle={styles.billStatusChipText}
                          icon={() => (
                            <MaterialCommunityIcons
                              name={getPaymentStatusIcon(bill.payment_status)}
                              size={16}
                              color={Colors.WHITE}
                            />
                          )}
                        >
                          {getPaymentStatusText(bill.payment_status)}
                        </Chip>
                      </View>
                    </View>

                    {/* Bill Actions */}
                    <Divider
                      style={[
                        styles.billCardDivider,
                        isDarkMode && styles.darkDivider,
                      ]}
                    />
                    <View style={styles.billCardActions}>
                      <TouchableOpacity
                        style={[
                          styles.billActionButton,
                          styles.viewActionButton,
                        ]}
                        onPress={() => handleBillPress(bill)}
                      >
                        <MaterialCommunityIcons
                          name="eye"
                          size={18}
                          color={Colors.PRIMARY}
                        />
                        <Text
                          style={[
                            styles.billActionText,
                            { color: Colors.PRIMARY },
                          ]}
                        >
                          {t("payment.view")}
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.billActionButton,
                          styles.editActionButton,
                        ]}
                        onPress={() => handleEditBill(bill)}
                      >
                        <MaterialCommunityIcons
                          name="pencil"
                          size={18}
                          color={Colors.SECONDARY}
                        />
                        <Text
                          style={[
                            styles.billActionText,
                            { color: Colors.SECONDARY },
                          ]}
                        >
                          {t("payment.edit")}
                        </Text>
                      </TouchableOpacity>

                      {bill.payment_status !== "paid" && (
                        <TouchableOpacity
                          style={[
                            styles.billActionButton,
                            styles.collectActionButton,
                          ]}
                          onPress={() => handleCollectPayment(bill.hoadon_id)}
                        >
                          <MaterialCommunityIcons
                            name="cash-check"
                            size={18}
                            color="#70C4D7"
                          />
                          <Text
                            style={[
                              styles.billActionText,
                              { color: "#70C4D7" },
                            ]}
                          >
                            {t("payment.collect")}
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </TouchableOpacity>
                </Surface>
              ))}
            </View>
          </>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      <IconButton
        icon="arrow-left"
        iconColor={Colors.WHITE}
        size={24}
        onPress={() => navigation.goBack()}
        style={styles.backButton}
      />
      <IconButton
        icon="delete"
        iconColor={Colors.WHITE}
        size={24}
        onPress={handleDeleteRoom}
        style={styles.deleteButton}
      />
      <ImageBackground
        source={{
          uri: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267",
        }}
        style={styles.coverImage}
      >
        <View style={[styles.overlay, isDarkMode && styles.darkOverlay]} />
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.roomNumber}>
            {t("room.roomLabel")} {room.room_number}
          </Text>
          <Chip
            style={[
              styles.statusChip,
              { backgroundColor: Colors.getStatusColor(room.status) },
            ]}
            textStyle={{ color: Colors.WHITE }}
          >
            {Colors.getStatusText(room.status)}
          </Chip>
        </View>
      </ImageBackground>

      <View style={[styles.tabBar, isDarkMode && styles.darkTabBar]}>
        <Button
          mode={activeTab === "info" ? "contained" : "text"}
          onPress={() => setActiveTab("info")}
          style={styles.tabButton}
          buttonColor={activeTab === "info" ? Colors.PRIMARY : undefined}
          textColor={
            activeTab !== "info" && isDarkMode
              ? Colors.DARK.TEXT_PRIMARY
              : undefined
          }
        >
          {t("room.information")}
        </Button>
        <Button
          mode={activeTab === "residents" ? "contained" : "text"}
          onPress={() => setActiveTab("residents")}
          style={styles.tabButton}
          buttonColor={activeTab === "residents" ? Colors.PRIMARY : undefined}
          textColor={
            activeTab !== "residents" && isDarkMode
              ? Colors.DARK.TEXT_PRIMARY
              : undefined
          }
        >
          {t("room.tenantsTab")}
        </Button>
        <Button
          mode={activeTab === "payments" ? "contained" : "text"}
          onPress={() => setActiveTab("payments")}
          style={styles.tabButton}
          buttonColor={activeTab === "payments" ? Colors.PRIMARY : undefined}
          textColor={
            activeTab !== "payments" && isDarkMode
              ? Colors.DARK.TEXT_PRIMARY
              : undefined
          }
        >
          {t("room.bills")}
        </Button>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === "info" && renderInfoTab()}
        {activeTab === "residents" && renderResidentsTab()}
        {activeTab === "payments" && renderPaymentsTab()}
      </ScrollView>

      <Modal
        visible={billDetailsVisible}
        onDismiss={() => setBillDetailsVisible(false)}
        contentContainerStyle={styles.modalContainer}
      >
        {selectedBill?.payment_status === "paid"
          ? renderInvoiceModal()
          : renderReceiptModal()}
      </Modal>

      {/* Resident CRUD Dialogs */}
      <ResidentDialog
        visible={residentDialogVisible}
        onDismiss={() => setResidentDialogVisible(false)}
        onSave={handleSaveResident}
        resident={selectedResident}
        mode={residentDialogMode}
      />

      <ConfirmDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={handleConfirmDeleteResident}
        title={t("resident.deleteResident")}
        message={t("resident.deleteConfirmMessage")}
        itemName={residentToDelete?.name}
      />

      <ConfirmDeleteDialog
        visible={deleteRoomDialogVisible}
        onDismiss={() => setDeleteRoomDialogVisible(false)}
        onConfirm={handleConfirmDeleteRoom}
        title={t("room.deleteRoom")}
        message={t("room.deleteRoomConfirmMessage")}
        itemName={room?.room_number}
      />

      {/* Collect Payment Dialog */}
      <CollectPaymentDialog
        visible={collectPaymentDialogVisible}
        onDismiss={() => setCollectPaymentDialogVisible(false)}
        onCollectPayment={handlePaymentCollected}
        bill={selectedBillForPayment}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
    position: "relative",
  },
  darkContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.BACKGROUND,
  },
  darkLoadingContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
  },
  darkLoadingText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  coverImage: {
    height: 240,
    width: "100%",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(99, 102, 241, 0.6)",
  },
  darkOverlay: {
    backgroundColor: "rgba(99, 102, 241, 0.7)",
  },
  header: {
    position: "absolute",
    bottom: 24,
    left: 24,
    right: 24,
  },
  backButton: {
    position: "absolute",
    zIndex: 1,
    top: 24,
    left: 12,
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  deleteButton: {
    position: "absolute",
    zIndex: 1,
    top: 24,
    right: 12,
    backgroundColor: "rgba(220,53,69,0.8)",
  },
  roomNumber: {
    color: Colors.WHITE,
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 12,
    letterSpacing: 0.5,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  statusChip: {
    alignSelf: "flex-start",
    borderRadius: Spacing.BORDER_RADIUS.lg,
    paddingHorizontal: Spacing.SPACING.md,
    paddingVertical: Spacing.SPACING.xs,
  },
  tabBar: {
    flexDirection: "row",
    backgroundColor: Colors.WHITE,
    paddingHorizontal: Spacing.SPACING.lg,
    paddingVertical: Spacing.SPACING.md,
    ...Spacing.SHADOW.md,
  },
  darkTabBar: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  tabButton: {
    flex: 1,
    margin: Spacing.SPACING.xs,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    height: 48,
  },
  content: {
    flex: 1,
    padding: Spacing.SPACING.lg,
    backgroundColor: Colors.BACKGROUND,
  },
  darkContent: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  tabContent: {
    gap: Spacing.SPACING.lg,
    marginBottom: 80,
  },
  infoSection: {
    padding: Spacing.SPACING.lg,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    backgroundColor: Colors.WHITE,
    ...Spacing.SHADOW.sm,
  },
  darkInfoSection: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  sectionTitle: {
    color: Colors.PRIMARY,
    fontWeight: "bold",
  },
  darkSectionTitle: {
    color: Colors.PRIMARY,
  },
  divider: {
    marginVertical: 12,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  darkDivider: {
    backgroundColor: Colors.DARK.DIVIDER,
  },
  infoGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
  },
  infoItem: {
    flex: 1,
    minWidth: "45%",
  },
  infoLabel: {
    color: Colors.TEXT_SECONDARY,
    marginBottom: 4,
  },
  darkInfoLabel: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
  },
  darkInfoValue: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  chip: {
    backgroundColor: Colors.GRAY_LIGHT,
    marginBottom: 8,
  },
  darkChip: {
    backgroundColor: Colors.DARK.GRAY_LIGHT,
  },
  emptyContainer: {
    padding: 32,
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
    alignItems: "center",
    justifyContent: "center",
  },
  darkEmptyContainer: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
  },
  darkEmptyText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  tableContainer: {
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: Colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  darkTableContainer: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  residentCard: {
    padding: Spacing.SPACING.lg,
    borderRadius: Spacing.BORDER_RADIUS.lg,
    backgroundColor: Colors.WHITE,
    marginBottom: Spacing.SPACING.md,
    ...Spacing.SHADOW.sm,
  },
  darkResidentCard: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  residentHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.SPACING.lg,
  },
  residentInfo: {
    flex: 1,
    gap: Spacing.SPACING.xs,
  },
  residentActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: Spacing.SPACING.sm,
    marginTop: Spacing.SPACING.md,
  },
  footerActionButton: {
    minWidth: 120,
    borderRadius: Spacing.BORDER_RADIUS.md,
  },
  footer: {
    padding: 16,
    flexDirection: "row",
    gap: 12,
    backgroundColor: Colors.WHITE,
  },
  darkFooter: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  footerEditButton: {
    flex: 1,
  },
  paymentButton: {
    flex: 1,
  },

  modalContainer: {
    position: "absolute",
    zIndex: 9999,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    backgroundColor: "rgba(0,0,0,0.7)",
  },
  modalContent: {
    width: "100%",
    maxWidth: 600,
    maxHeight: "90%",
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: Colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  darkModalContent: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 8,
    paddingLeft: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_LIGHT,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.PRIMARY,
  },
  darkModalTitle: {
    color: Colors.PRIMARY,
  },
  modalScrollContent: {
    padding: 4,
  },
  billDetailsContent: {
    padding: 4,
    borderRadius: 12,
  },
  billHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  billMonth: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
  },
  darkBillMonth: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  billDate: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY,
  },
  darkBillDate: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  billSection: {
    marginBottom: 20,
    padding: 20,
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
    borderWidth: 1,
    borderColor: Colors.GRAY_LIGHT,
    ...Platform.select({
      ios: {
        shadowColor: Colors.BLACK,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  darkBillSection: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
    borderColor: Colors.DARK.BORDER,
  },
  billRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
  },
  billLabel: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY,
  },
  darkBillLabel: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  billValue: {
    fontSize: 14,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
  },
  darkBillValue: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  totalDivider: {
    marginVertical: 12,
    height: 1,
    backgroundColor: Colors.PRIMARY,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
  },
  darkTotalLabel: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.PRIMARY,
  },
  darkTotalValue: {
    color: Colors.PRIMARY,
  },
  billActionButtonsContainer: {
    marginTop: 24,
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
    borderRadius: 8,
    height: 48,
    justifyContent: "center",
    width: "48%",
  },

  summaryContainer: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
  },
  darkSummaryContainer: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 16,
  },
  darkSummaryTitle: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 4,
  },
  darkStatValue: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.TEXT_SECONDARY,
  },
  darkStatLabel: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  progressBarContainer: {
    marginTop: 8,
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.GRAY_LIGHT,
    borderRadius: 4,
    overflow: "hidden",
    flexDirection: "row",
  },
  progressBarSegment: {
    height: "100%",
  },

  filterContainer: {
    marginBottom: 16,
  },
  filterChip: {
    marginRight: 8,
    backgroundColor: Colors.GRAY_LIGHT,
    borderWidth: 1,
    borderColor: Colors.GRAY_MEDIUM,
    height: 36,
    paddingHorizontal: 12,
  },
  darkFilterChip: {
    backgroundColor: Colors.DARK.GRAY_LIGHT,
    borderColor: Colors.DARK.BORDER,
  },

  tableHeader: {
    backgroundColor: Colors.GRAY_LIGHT,
    height: 48,
  },
  tableHeaderText: {
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
  },
  darkTableHeaderText: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_LIGHT,
    height: 56,
  },
  tableRowEven: {
    backgroundColor: "rgba(0,0,0,0.02)",
  },
  darkTableRow: {
    borderBottomColor: Colors.DARK.BORDER,
  },
  darkTableRowEven: {
    backgroundColor: "rgba(255,255,255,0.05)",
  },
  tableCell: {
    fontSize: 14,
    color: Colors.TEXT_PRIMARY,
  },
  tableCellAmount: {
    fontWeight: "bold",
  },
  darkTableCell: {
    color: Colors.DARK.TEXT_PRIMARY,
  },

  billsContainer: {
    gap: 16,
  },
  billCard: {
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    overflow: "hidden",
    marginBottom: 8,
  },
  darkBillCard: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  billCardContent: {
    padding: 20,
  },
  billCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  billCardLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  billIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${Colors.PRIMARY}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  billCardInfo: {
    flex: 1,
  },
  billCardMonth: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 4,
  },
  darkBillCardMonth: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  billCardDate: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY,
  },
  darkBillCardDate: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  billCardRight: {
    alignItems: "flex-end",
    gap: 8,
  },
  billCardAmount: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.PRIMARY,
  },
  darkBillCardAmount: {
    color: Colors.PRIMARY,
  },
  billStatusChip: {
    height: 32,
    paddingHorizontal: 12,
  },
  billStatusChipText: {
    color: Colors.WHITE,
    fontSize: 12,
    fontWeight: "bold",
  },
  billCardDivider: {
    marginVertical: 16,
  },
  billCardActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    gap: 12,
  },
  billActionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Spacing.SPACING.md,
    paddingHorizontal: Spacing.SPACING.lg,
    borderRadius: Spacing.BORDER_RADIUS.md,
    backgroundColor: Colors.GRAY_LIGHT,
    gap: Spacing.SPACING.xs,
    ...Spacing.SHADOW.xs,
  },
  viewActionButton: {
    backgroundColor: `${Colors.PRIMARY}15`,
  },
  editActionButton: {
    backgroundColor: `${Colors.SECONDARY}15`,
  },
  collectActionButton: {
    backgroundColor: `${Colors.ACCENT}15`,
  },
  billActionText: {
    fontSize: 14,
    fontWeight: "600",
  },

  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "bold",
  },
  viewButton: {
    margin: 0,
  },
  actionButtonsContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 24,
  },

  sectionHeaderWithButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  addButton: {
    marginBottom: 16,
    borderRadius: 8,
  },
  addBillContainer: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  addBillButton: {
    borderRadius: 8,
  },
  editButton: {
    borderColor: "#70C4D7",
    borderRadius: 6,
  },
  editButtonLabel: {
    color: "#70C4D7",
    fontSize: 12,
    fontWeight: "600",
  },
  // Invoice Modal Styles (Professional Black & White)
  invoiceModalContent: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    margin: 15,
    borderRadius: 0,
    padding: 8,
    maxHeight: "92%",
    borderWidth: 2,
    borderColor: "#000000",
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  invoiceModalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#000000",
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginBottom: 0,
  },
  invoiceModalTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#FFFFFF",
    letterSpacing: 1,
  },
  invoiceBillDetailsContent: {
    backgroundColor: "#FFFFFF",
    padding: 8,
    borderWidth: 0,
  },
  invoiceBillHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 25,
    paddingBottom: 20,
    borderBottomWidth: 3,
    borderBottomColor: "#000000",
  },
  invoiceBillMonth: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  invoiceBillDate: {
    fontSize: 16,
    color: "#666666",
    fontStyle: "italic",
  },
  invoiceStatusContainer: {
    backgroundColor: "#000000",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 0,
    borderWidth: 2,
    borderColor: "#000000",
  },
  invoiceStatusText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
    letterSpacing: 0.5,
  },
  invoiceStatusValue: {
    color: "#000000",
    fontSize: 16,
    fontWeight: "bold",
  },
  invoiceBillSection: {
    marginBottom: 25,
    paddingVertical: 20,
    borderWidth: 2,
    borderColor: "#000000",
    backgroundColor: "#FAFAFA",
    paddingHorizontal: 20,
    borderRadius: 0,
  },
  invoiceSectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 15,
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  invoiceDivider: {
    height: 2,
    backgroundColor: "#000000",
    marginVertical: 12,
  },
  invoiceBillRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  invoiceBillLabel: {
    fontSize: 16,
    color: "#000000",
    flex: 1,
    fontWeight: "500",
  },
  invoiceBillValue: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "600",
    textAlign: "right",
    minWidth: 100,
  },
  invoiceTotalLabel: {
    fontSize: 20,
    color: "#000000",
    fontWeight: "bold",
    flex: 1,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  invoiceTotalValue: {
    fontSize: 20,
    color: "#000000",
    fontWeight: "bold",
    textAlign: "right",
    minWidth: 120,
  },
  invoiceActionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 25,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: "#F5F5F5",
    borderTopWidth: 2,
    borderTopColor: "#000000",
    gap: 15,
  },
  invoiceActionButton: {
    borderWidth: 2,
    borderColor: "#000000",
    borderRadius: 0,
    paddingVertical: 12,
    width: "48%",
  },
});
