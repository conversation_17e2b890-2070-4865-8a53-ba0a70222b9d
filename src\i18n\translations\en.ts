export default {
  common: {
    yes: "Yes",
    no: "No",
    cancel: "Cancel",
    confirm: "Confirm",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    add: "Add",
    update: "Update",
    back: "Back",
    next: "Next",
    done: "Done",
    reset: "Reset",
    apply: "Apply",
    success: "Success",
    error: "Error",
    deleteWarning: "This action cannot be undone.",
  },
  auth: {
    welcomeBack: "Welcome back!",
    createAccount: "Create an account",
    or: "Or",
    login: "Login",
    signup: "Sign up",
    logout: "Logout",
    dontHaveAccount: "Don't have an account?",
    forgotPassword: "Forgot Password",
    email: "Email",
    password: "Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmPassword: "Confirm New Password",
    changePassword: "Change Password",
    otpVerification: "OTP Verification",
    otpVerificationSuccess: "Verification Successful!",
    enterOtpSent: "Please enter the 6-digit OTP code sent to",
    verify: "Verify",
    noCodeReceived: "Didn't receive the code?",
    resend: "Resend",
    accountVerified: "Your account has been successfully verified.",
    passwordReset: "Your password has been successfully reset.",
    passwordPlaceholder: "Enter your password",

    // Google OAuth
    signInWithGoogle: "Sign in with Google",
    signUpWithGoogle: "Sign up with Google",
    googleSignInCancelled: "Google sign-in was cancelled",
    googleSignInInProgress: "Google sign-in is already in progress",
    playServicesNotAvailable: "Google Play Services not available",
    googleSignInError: "Failed to sign in with Google",
    googleSignInSuccess: "Successfully signed in with Google",
    accountCreatedWithGoogle: "Account created successfully with Google",
    logoutError: "An error occurred while logging out. Please try again.",
    signupSuccess: "Sign Up Successful",
    signupSuccessMessage:
      "Your account has been verified successfully. Please log in to continue.",
    verificationSuccess: "Verification Successful",
    passwordResetSuccess:
      "Your password has been reset. Please check your email for the new password.",
    verificationError: "An error occurred",
    emailAlreadyInUse: "This email is already in use",
    otpResent: "OTP Resent",
    otpResentMessage: "New OTP code has been sent to {email}",
    emailRequired: "Please enter your email",
    invalidEmail: "Invalid email",
    resetPasswordError: "An error occurred while sending password reset email",
    userNotFound: "No account found with this email",
    emailSent: "Email sent!",
    resetInstructions:
      "We have sent password reset instructions to your email. Please check your inbox.",
    backToLogin: "Back to login",
    emailPlaceholder: "Enter your email",
    resetPassword: "Reset Password",
    vietnamese: "Tiếng Việt",
    english: "English",
    signupError: "An error occurred during sign up",
    weakPassword: "Password must be at least 6 characters",
    fullName: "Full Name",
    fullNamePlaceholder: "Enter your full name",
    confirmPasswordPlaceholder: "Re-enter your password",
    alreadyHaveAccount: "Already have an account?",
  },
  profile: {
    profile: "Profile",
    building: "Building",
    editHistory: "Edit History",
    privacyPolicy: "Privacy Policy",
    changePassword: "Change Password",
    language: "Language",
    darkMode: "Dark Mode",
    termsOfUse: "Terms of Use",
    version: "Version",
    role: "Manager",
    defaultPrices: "Default Prices",
    defaultElectricityPrice: "Default Electricity Price (VND/kWh)",
    defaultWaterPrice: "Default Water Price (VND/m³)",
    defaultPricesUpdated: "Default prices updated successfully",
    defaultServices: "Default Services",
    addDefaultService: "Add Default Service",
    serviceName: "Service Name",
    servicePrice: "Service Price (VND)",
    editService: "Edit Service",
    deleteService: "Delete Service",
    confirmDeleteService: "Are you sure you want to delete this service?",
    serviceNameRequired: "Service name cannot be empty",
    servicePriceRequired: "Service price must be greater than 0",
    resetToDefault: "Reset to Default",
    addServiceTitle: "Add New Service",
  },
  motel: {
    addMotel: "Add New Motel",
    editMotel: "Edit Motel",
    motelName: "Motel Name",
    address: "Address",
    basicInfo: "Basic Information",
    sharedManagement: "Shared Management",
    sharedManagementDesc: "Add others to co-manage the motel",
    addManager: "Add Manager",
    removeManager: "Are you sure you want to remove this manager?",
    emailRequired: "Please enter email",
    emailInvalid: "Invalid email",
    emailExists: "This email has already been added",
    nameRequired: "Please enter motel name",
    addressRequired: "Please enter address",
    statusAccepted: "Accepted",
    statusPending: "Pending",
    statusInvalid: "Email does not exist",
    createMotel: "Create Motel",
    updateMotel: "Update",
    confirmDelete: "Confirm Delete",
    deleteConfirmMessage: "Are you sure you want to delete this motel?",
    emailPlaceholder: "<EMAIL>",
    noManagers: "No managers yet",
    addFirstManager: "Add first manager",
    switchMotel: "Switch Motel",
    switchMotelConfirm: 'Do you want to switch to managing motel "{name}"?',
    switch: "Switch",
    totalRooms: "Total Rooms",
    empty: "Empty",
    rented: "Rented",
    maintenance: "Maintenance",
    update: "Update",
    loading: "Loading data...",
    noMotelsFound: "No motels found",
    searchPlaceholder: "Search motels...",
    management: "Motel Management",
    statusActive: "Active",
    statusConstruction: "Under Construction",
    statusMaintenance: "Under Maintenance",
    statusUnknown: "Unknown",
  },
  report: {
    statistics: "Statistics",
    operations: "Operations",
    finance: "Finance",
    room: "Room",
    building: "Building",
    revenue: "Revenue",
    expenses: "Expenses",
    datePeriod: "Period",
    lastUpdated: "Last updated at",
    date: "Date",
    roomManagement: "Room Management",
    all: "All",
    overdue: "Overdue",
    paid: "Paid",
    repair: "Repair",
    empty: "Empty",
    rooms: "rooms",
    query: "Query",
    tenantManagement: "Tenant Management",
    tenantInfo: "Quick view of tenant information and contracts",
    expired: "Paid",
    expiringSoon: "Expiring Soon",
    renewed: "Renewed",
    overview: "Overview",
    rented: "Rented",
    underRepair: "Under Repair",
    movingOut: "Moving Out",
    chart: "Chart",
    maintenanceHistory: "Maintenance History",
    completed: "Completed",
    inProgress: "In Progress",
    totalRevenue: "Total Revenue",
    totalExpenses: "Total Expenses",
    electricity: "Electricity",
    water: "Water",
    maintenance: "Maintenance",
    other: "Other",
    staffSalary: "Staff Salary",
    renovation: "Renovation",
    newContracts: "New Contracts",
    onTimePayment: "On-time Payment",
    reservedRooms: "Reserved Rooms",
    percentValue: "%",
    maintenanceSchedule: "Maintenance Schedule",
    category: "Category",
  },
  room: {
    addRoom: "Add New Room",
    editRoom: "Edit Room",
    roomDetails: "Room Details",
    roomNumber: "Room Number",
    roomNumberPlaceholder: "Enter room number",
    floor: "Floor",
    area: "Area",
    areaPlaceholder: "Enter area",
    price: "Price",
    pricePlaceholder: "Enter price",
    status: "Status",
    utilities: "Utilities",
    tenants: "Tenants",
    rooms: "rooms",
    searchPlaceholder: "Search rooms...",
    activeFilters: "Filters",
    clearAllFilters: "Clear all",
    noRoomsFound: "No rooms found",
    filterStatus: "Room Status",
    filterType: "Room Type",
    filterPrice: "Price Range",
    filterAmenities: "Amenities",
    filterMinPrice: "Minimum",
    filterMaxPrice: "Maximum",
    statusAll: "All",
    statusOccupied: "Occupied",
    statusAvailable: "Available",
    statusMaintenance: "Under Maintenance",
    available: "Available",

    loadingDetails: "Loading room details...",
    basicInfo: "Basic Information",
    roomType: "Room Type",
    amenities: "Amenities",
    rentalPrice: "Rental Price",
    deposit: "Deposit",
    depositPlaceholder: "Enter deposit",
    notes: "Notes",
    notesPlaceholder: "Enter notes",
    noNotes: "No notes",
    roomLabel: "Room",
    month: "month",
    information: "Information",
    tenantsTab: "Tenants",
    bills: "Bills",
    noTenants: "No tenants",
    mainTenant: "Main Tenant",
    resident: "Resident",
    call: "Call",
    message: "Message",
    noBills: "No bills",
    collectPayment: "Collect Payment",
    roomFee: "Room Fee",
    total: "Total",

    // Phone call functionality
    confirmCall: "Confirm Call",
    confirmCallMessage: "Do you want to call {{name}} at {{phone}}?",
    invalidPhoneNumber: "Invalid phone number",
    callError: "Unable to make phone call",
    phoneNotSupported: "Phone calls are not supported on this device",
    smsError: "Unable to open SMS app",
    smsNotSupported: "SMS is not supported on this device",

    roomInfo: "Room Information",
    roomPrice: "Room Price",
    capacity: "Capacity",
    capacityPlaceholder: "Enter capacity",
    people: "people",
    services: "Services",
    pricingAndServices: "Pricing & Services",
    areaWithUnit: "Area (m²)",
    roomPriceVND: "Room Price (VND)",
    electricityPriceUnit: "Electricity Price (VND/kWh)",
    waterPriceUnit: "Water Price (VND/m³)",
    addRoomAction: "Add Room",
    addSuccess: "Room added successfully",
    updateSuccess: "Room updated successfully",
    updateError: "Failed to update room",
    selectRoomType: "Select Room Type",
    selectAmenities: "Select Amenities",
    invalidRoomRange: "Invalid room range",

    smartAdd: "Smart Room Addition",
    smartAddDescription: "Create multiple rooms with the same properties",
    configureSmartAdd: "Configure Smart Addition",
    bulkCreation: "Bulk Room Creation",
    bulkCreationDescription: "Create multiple rooms with the same properties",
    roomPrefix: "Room Prefix",
    roomSuffix: "Room Suffix",
    startNumber: "Start Number",
    endNumber: "End Number",
    previewRooms: "Preview Rooms",
    roomsToCreate: "Rooms to create: {count}",
    selectAll: "Select All",
    deselectAll: "Deselect All",
    selectedRooms: "Selected Rooms",
    noRoomsSelected: "No rooms selected",
    useExistingRoomSettings: "Use settings from Room",
    roomPreview: "Room Preview",
    addMultipleRooms: "Add Multiple Rooms",
    bulkAddSuccess: "Successfully added {count} rooms",
    roomNumberExists: "Room number {number} already exists",
    addSingleRoom: "Add Single Room",
    selected: "selected",
    bulkAddConfirmation:
      "You are about to add {count} rooms with the following information: {roomInfo}",
    importFromExcel: "Import from Excel",
    maintenanceConfirm: "Switch room to maintenance mode?",
    maintenanceDesc: "Room will not be available for rent during maintenance.",
    maintenanceSuccess: "Switched to maintenance mode.",
    stopMaintenance: "Stop maintenance",
    stopMaintenanceConfirm: "Stop maintenance for this room?",
    stopMaintenanceSuccess: "Stopped maintenance.",
    stopMaintenanceDesc: "Room will return to available status.",
    cannotSetMaintenanceWithTenants:
      "Cannot set maintenance mode for occupied rooms. Please move all tenants out before maintenance.",
    deleteRoom: "Delete Room",
    deleteRoomConfirmMessage:
      "Are you sure you want to delete this room? This action cannot be undone.",
    deleteSuccess: "Room deleted successfully",
    deleteError: "Failed to delete room",
  },
  validation: {
    required: "This field is required",
    mustBeNumber: "Must be a number",
    invalidRange: "Start number must be less than end number",
    noRoomsSelected: "Please select at least one room",
    selectRoomType: "Please select room type",
    selectAmenities: "Please select amenities",
    available: "Available",
    maintenance: "Under maintenance",
    addSelection: "Add selection",
    modifySelection: "Modify selection",
    invalidPrice: "Invalid price",
    invalidDeposit: "Invalid deposit",
    invalidArea: "Invalid area",
    invalidCapacity: "Invalid capacity",
    tooManyRooms: "Too many rooms (maximum 100)",
    invalidPhone: "Invalid phone number",
    addRoomError: "An error occurred while adding room. Please try again.",
    importSuccess: "Successfully imported {count} rooms from Excel file!",
    addRoomDescription: "Add new room: {roomNumber}",
    phoneNumber: "Phone Number",
    saveChanges: "Save Changes",
  },
  editHistory: {
    addTenant: "Add tenant",
    addTenantWithRoom: "Add tenant (room {roomName})",
    addBill: "Add bill",
    addBillWithRoom: "Add bill (room {roomName})",
    addRoom: "Add room",
    addRoomWithName: "Add room {roomName}",
    addData: "Add data",
    updateTenant: "Update tenant",
    updateTenantWithRoom: "Update tenant (room {roomName})",
    updateBill: "Update bill",
    updateBillWithRoom: "Update bill (room {roomName})",
    updateRoom: "Update room",
    updateRoomWithName: "Update room {roomName}",
    updateData: "Update data",
    deleteTenant: "Delete tenant",
    deleteTenantWithRoom: "Delete tenant (room {roomName})",
    deleteBill: "Delete bill",
    deleteBillWithRoom: "Delete bill (room {roomName})",
    deleteRoom: "Delete room",
    deleteRoomWithName: "Delete room {roomName}",
    deleteData: "Delete data",
  },
  navigation: {
    home: "Home",
    report: "Report",
    profile: "Profile",
  },
  resident: {
    addResident: "Add Resident",
    editResident: "Edit Resident",
    deleteResident: "Delete Resident",
    name: "Full Name",
    phone: "Phone Number",
    identityNumber: "Identity Number",
    isMainTenant: "Main Tenant",
    addSuccess: "Resident added successfully",
    updateSuccess: "Resident updated successfully",
    deleteSuccess: "Resident deleted successfully",
    deleteConfirmMessage: "Are you sure you want to delete this resident?",
  },
  roomTypes: {
    single: "Single Room",
    double: "Double Room",
    withLoft: "With Loft",
    withoutLoft: "Without Loft",
    studio: "Studio",
  },
  amenities: {
    airConditioner: "Air Conditioner",
    refrigerator: "Refrigerator",
    washingMachine: "Washing Machine",
    tv: "TV",
    kitchen: "Kitchen",
    balcony: "Balcony",
    wifi: "Wifi",
    hotWater: "Hot Water",
    wardrobe: "Wardrobe",
    bed: "Bed",
  },
  payment: {
    billDetails: "Bill Details",
    summary: "Summary",
    paymentDetails: "Payment Details",
    electricity: "Electricity",
    water: "Water",
    dueDate: "Due Date",
    status: "Status",
    paid: "Paid",
    pending: "Pending",
    overdue: "Overdue",
    unknown: "Unknown",
    overdueDays: "Overdue Days",
    days: "days",
    markAsPaid: "Mark as Paid",
    confirmMarkAsPaid: "Are you sure you want to mark this bill as paid?",
    success: "Success",
    paymentRecorded: "Payment has been recorded",
    printReceipt: "Print Receipt",
    receiptGenerated: "Receipt has been generated",
    printSuccess: "Receipt Generated",
    receiptSaved: "Receipt has been saved to your device",
    printError: "Error",
    printErrorMessage: "Failed to generate receipt",
    exportBill: "Export Bill",
    billExported: "Bill has been exported",
    exportSuccess: "Export Successful",
    exportError: "Export Error",
    exportErrorMessage: "Failed to export bill",
    save: "Save",
    share: "Share",
    saveSuccess: "Save Successful",
    saveError: "Save Error",
    saveErrorMessage: "Failed to save bill",
    shareError: "Share Error",
    shareErrorMessage: "Failed to share bill",
    billSavedToGallery: "Bill has been saved to your gallery",
    storagePermissionRequired:
      "Storage permission is required to save the bill",
    billFor: "Bill for",
    receiptFor: "Receipt for",
    sendReminder: "Send Reminder",
    reminderSent: "Payment reminder has been sent",
    createdOn: "Created on",
    totalBilled: "Total Billed",
    outstanding: "Outstanding",
    all: "All",
    actions: "Actions",
    collectPayment: "Collect Payment",
    paymentAmount: "Payment Amount",
    quickAmount: "Quick Amount",
    fullAmount: "Full Amount",
    halfAmount: "Half Amount",
    paymentMethod: "Payment Method",
    paymentNote: "Payment Note",
    paymentNotePlaceholder: "Enter payment note (optional)",
    cash: "Cash",
    bankTransfer: "Bank Transfer",
    momo: "MoMo",
    other: "Other",
    amountExceedsBill: "Amount cannot exceed bill total",
    addBill: "Add Bill",
    view: "View",
    edit: "Edit",
    collect: "Collect",
    updateStatusSuccess: "Payment status updated successfully!",
    updateStatusFailed: "Failed to update payment status!",
    collectError: "Error occurred while collecting payment!",
    receipt: "Receipt",
    invoice: "Invoice",
    paidOn: "Paid on",
    paymentDate: "Payment Date",
  },
};
