// Modern Color palette for the application

// Primary color - keeping user's preferred #70C4D7 with enhancements
export const PRIMARY = "#70C4D7";
export const PRIMARY_DARK = "#5BA3B8";
export const PRIMARY_LIGHT = "#8DD1E0";

// Modern complementary colors
export const SECONDARY = "#FF6B6B"; // Vibrant coral
export const ACCENT = "#4ECDC4"; // Teal accent
export const TERTIARY = "#45B7D1"; // Sky blue

// Additional brand colors
export const SUCCESS_GRADIENT = ["#56CCF2", "#2F80ED"];
export const WARNING_GRADIENT = ["#FFD93D", "#FF9500"];
export const ERROR_GRADIENT = ["#FF6B6B", "#EE5A52"];

// Modern Light theme colors
export const LIGHT = {
  WHITE: "#FFFFFF",
  BLACK: "#000000",
  GRAY_LIGHT: "#F8FAFC",
  GRAY_MEDIUM: "#E2E8F0",
  GRAY_DARK: "#64748B",
  BACKGROUND: "#F1F5F9", // Softer background
  TEXT_PRIMARY: "#1E293B", // Warmer dark
  TEXT_SECONDARY: "#64748B", // Better contrast
  TEXT_HINT: "#94A3B8",
  CARD_BACKGROUND: "#FFFFFF",
  BORDER: "#E2E8F0",
  HEADER_BACKGROUND: "#FFFFFF",
  SURFACE: "#FFFFFF",
  DIVIDER: "#E2E8F0",
  OVERLAY: "rgba(0, 0, 0, 0.5)",
  SHADOW: "rgba(0, 0, 0, 0.1)",
};

// Modern Dark theme colors
export const DARK = {
  WHITE: "#FFFFFF",
  BLACK: "#000000",
  GRAY_LIGHT: "#334155",
  GRAY_MEDIUM: "#475569",
  GRAY_DARK: "#64748B",
  BACKGROUND: "#0F172A", // Deep navy background
  TEXT_PRIMARY: "#F1F5F9", // Softer white
  TEXT_SECONDARY: "#CBD5E1", // Better contrast
  TEXT_HINT: "#94A3B8",
  CARD_BACKGROUND: "#1E293B", // Warmer dark cards
  BORDER: "#334155",
  HEADER_BACKGROUND: "#1E293B",
  SURFACE: "#1E293B",
  DIVIDER: "#334155",
  OVERLAY: "rgba(0, 0, 0, 0.7)",
  SHADOW: "rgba(0, 0, 0, 0.3)",
};

// For backward compatibility, export light theme as default
export const WHITE = LIGHT.WHITE;
export const BLACK = LIGHT.BLACK;
export const GRAY_LIGHT = LIGHT.GRAY_LIGHT;
export const GRAY_MEDIUM = LIGHT.GRAY_MEDIUM;
export const GRAY_DARK = LIGHT.GRAY_DARK;
export const BACKGROUND = LIGHT.BACKGROUND;
export const TEXT_PRIMARY = LIGHT.TEXT_PRIMARY;
export const TEXT_SECONDARY = LIGHT.TEXT_SECONDARY;
export const TEXT_HINT = LIGHT.TEXT_HINT;

// Modern Status colors
export const SUCCESS = "#10B981"; // Emerald
export const INFO = "#3B82F6"; // Blue
export const WARNING = "#F59E0B"; // Amber
export const DANGER = "#EF4444"; // Red
export const AVAILABLE = "#06B6D4"; // Cyan
export const OCCUPIED = "#10B981"; // Emerald
export const MAINTENANCE = "#F59E0B"; // Amber

// Payment status colors
export const PAYMENT_PAID = "#10B981"; // Emerald for paid
export const PAYMENT_PENDING = "#F59E0B"; // Amber for pending
export const PAYMENT_OVERDUE = "#EF4444"; // Red for overdue
export const PAYMENT_PARTIAL = "#70C4D7"; // Primary color for partial payment

// Modern Gradients
export const GRADIENT_PRIMARY = [PRIMARY, PRIMARY_DARK];
export const GRADIENT_SECONDARY = [SECONDARY, "#FF5252"];
export const GRADIENT_SUCCESS = ["#10B981", "#059669"];
export const GRADIENT_WARNING = ["#F59E0B", "#D97706"];
export const GRADIENT_DANGER = ["#EF4444", "#DC2626"];
export const GRADIENT_INFO = ["#3B82F6", "#2563EB"];

// Background gradients
export const GRADIENT_BACKGROUND_LIGHT = ["#F8FAFC", "#E2E8F0"];
export const GRADIENT_BACKGROUND_DARK = ["#0F172A", "#1E293B"];

// Get status color based on room status
export const getStatusColor = (status: string): string => {
  switch (status) {
    case "occupied":
      return OCCUPIED;
    case "available":
      return AVAILABLE;
    case "maintenance":
      return MAINTENANCE;
    default:
      return GRAY_DARK;
  }
};

// Get status text based on room status
export const getStatusText = (status: string): string => {
  switch (status) {
    case "occupied":
      return "Đã cho thuê";
    case "available":
      return "Trống";
    case "maintenance":
      return "Đang sửa chữa";
    default:
      return "Không xác định";
  }
};
