// Color palette for the application

// Primary color as specified
export const PRIMARY = "#006eff";

// Generated complementary colors
export const SECONDARY = "#D77C70"; // Complementary to primary
export const ACCENT = "#70D78C"; // Analogous to primary

// Light theme colors
export const LIGHT = {
  WHITE: "#FFFFFF",
  BLACK: "#000000",
  GRAY_LIGHT: "#F5F5F5",
  GRAY_MEDIUM: "#E0E0E0",
  GRAY_DARK: "#9E9E9E",
  BACKGROUND: "#FAFAFA",
  TEXT_PRIMARY: "#212121",
  TEXT_SECONDARY: "#757575",
  TEXT_HINT: "#9E9E9E",
  CARD_BACKGROUND: "#FFFFFF",
  BORDER: "#E0E0E0",
  HEADER_BACKGROUND: "#FFFFFF",
  SURFACE: "#FFFFFF",
  DIVIDER: "#E0E0E0",
};

// Dark theme colors
export const DARK = {
  WHITE: "#FFFFFF",
  BLACK: "#000000",
  GRAY_LIGHT: "#2C2C2C",
  GRAY_MEDIUM: "#3D3D3D",
  GRAY_DARK: "#707070",
  BACKGROUND: "#121212",
  TEXT_PRIMARY: "#F5F5F5",
  TEXT_SECONDARY: "#BABABA",
  TEXT_HINT: "#808080",
  CARD_BACKGROUND: "#1E1E1E",
  BORDER: "#383838",
  HEADER_BACKGROUND: "#1A1A1A",
  SURFACE: "#1E1E1E",
  DIVIDER: "#383838",
};

// For backward compatibility, export light theme as default
export const WHITE = LIGHT.WHITE;
export const BLACK = LIGHT.BLACK;
export const GRAY_LIGHT = LIGHT.GRAY_LIGHT;
export const GRAY_MEDIUM = LIGHT.GRAY_MEDIUM;
export const GRAY_DARK = LIGHT.GRAY_DARK;
export const BACKGROUND = LIGHT.BACKGROUND;
export const TEXT_PRIMARY = LIGHT.TEXT_PRIMARY;
export const TEXT_SECONDARY = LIGHT.TEXT_SECONDARY;
export const TEXT_HINT = LIGHT.TEXT_HINT;

// Status colors
export const SUCCESS = "#28A745";
export const INFO = "#17A2B8";
export const WARNING = "#FFC107";
export const DANGER = "#DC3545";
export const AVAILABLE = "#17A2B8";
export const OCCUPIED = "#28A745";
export const MAINTENANCE = "#FFC107";

// Payment status colors
export const PAYMENT_PAID = "#28A745"; // Green for paid
export const PAYMENT_PENDING = "#FFC107"; // Yellow for pending
export const PAYMENT_OVERDUE = "#DC3545"; // Red for overdue
export const PAYMENT_PARTIAL = "#70C4D7"; // Primary color for partial payment

// Gradients
export const GRADIENT_PRIMARY = [PRIMARY, "#5EAFC2"];
export const GRADIENT_SECONDARY = [SECONDARY, "#C56A5E"];

// Get status color based on room status
export const getStatusColor = (status: string): string => {
  switch (status) {
    case "occupied":
      return OCCUPIED;
    case "available":
      return AVAILABLE;
    case "maintenance":
      return MAINTENANCE;
    default:
      return GRAY_DARK;
  }
};

// Get status text based on room status
export const getStatusText = (status: string): string => {
  switch (status) {
    case "occupied":
      return "Đã cho thuê";
    case "available":
      return "Trống";
    case "maintenance":
      return "Đang sửa chữa";
    default:
      return "Không xác định";
  }
};
