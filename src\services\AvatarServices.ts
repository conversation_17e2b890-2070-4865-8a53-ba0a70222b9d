import { auth, storage, db } from '../../config/firebase';
import { ref, uploadBytes, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { doc, updateDoc } from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import * as ImagePicker from 'expo-image-picker';

export interface AvatarUploadResult {
  success: boolean;
  message: string;
  imageUrl?: string;
}

// <PERSON><PERSON>u cầu quyền truy cập camera và thư viện ảnh
export const requestPermissions = async (): Promise<boolean> => {
  try {
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
    const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted';
  } catch (error) {
    console.error('Error requesting permissions:', error);
    return false;
  }
};

// <PERSON><PERSON><PERSON>nh từ thư viện
export const pickImageFromLibrary = async (): Promise<ImagePicker.ImagePickerResult | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      return null;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    return result;
  } catch (error) {
    console.error('Error picking image from library:', error);
    return null;
  }
};

// Chụp ảnh từ camera
export const takePhotoFromCamera = async (): Promise<ImagePicker.ImagePickerResult | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      return null;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    return result;
  } catch (error) {
    console.error('Error taking photo from camera:', error);
    return null;
  }
};

// Upload ảnh lên Firebase Storage
export const uploadImageToStorage = async (imageUri: string, userId: string): Promise<string | null> => {
  try {
    console.log('Starting image upload for user:', userId);
    console.log('Image URI:', imageUri);
    
    // Thử với XMLHttpRequest thay vì fetch
    const response = await fetch(imageUri);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }
    
    const blob = await response.blob();
    console.log('Blob created, size:', blob.size);
    
    // Giảm kích thước blob nếu quá lớn (>2MB)
    let finalBlob = blob;
    if (blob.size > 2 * 1024 * 1024) {
      console.log('Blob too large, need to resize...');
      // Tạm thời skip resize, chỉ cảnh báo
      console.warn('Image size is large:', blob.size, 'bytes');
    }
    
    const timestamp = Date.now();
    const fileName = `avatar_${timestamp}.jpg`;
    const storagePath = `avatars/${userId}/${fileName}`;
    
    console.log('Creating storage reference for path:', storagePath);
    const imageRef = ref(storage, storagePath);
    
    console.log('Starting upload...');
    
    // Thử upload với metadata
    const metadata = {
      contentType: 'image/jpeg',
      customMetadata: {
        'uploadedBy': userId,
        'uploadedAt': timestamp.toString()
      }
    };
    
    // Thử với uploadBytesResumable thay vì uploadBytes
    console.log('Using resumable upload...');
    const uploadTask = uploadBytesResumable(imageRef, finalBlob, metadata);
    
    // Tạo promise để handle upload task
    const snapshot = await new Promise<any>((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          console.log('Upload progress:', progress + '%');
        },
        (error) => {
          console.error('Upload error:', error);
          reject(error);
        },
        () => {
          console.log('Upload completed');
          resolve(uploadTask.snapshot);
        }
      );
    });
    
    console.log('Upload successful, snapshot metadata:', snapshot.metadata);
    
    console.log('Getting download URL...');
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL obtained:', downloadURL);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    // Log thêm thông tin về error
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('Firebase error code:', (error as any).code);
    }
    
    return null;
  }
};

// Upload ảnh lên Firebase Storage (fallback method)
export const uploadImageToStorageSimple = async (imageUri: string, userId: string): Promise<string | null> => {
  try {
    console.log('Trying simple upload method...');
    
    const response = await fetch(imageUri);
    const blob = await response.blob();
    
    // Sử dụng path đơn giản hơn
    const fileName = `${userId}_${Date.now()}.jpg`;
    const imageRef = ref(storage, `avatars/${fileName}`);
    
    console.log('Simple upload to:', `avatars/${fileName}`);
    
    const snapshot = await uploadBytes(imageRef, blob);
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    console.log('Simple upload successful:', downloadURL);
    return downloadURL;
  } catch (error) {
    console.error('Simple upload failed:', error);
    return null;
  }
};

// Cập nhật avatar của user
export const updateUserAvatar = async (imageUrl: string): Promise<AvatarUploadResult> => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return {
        success: false,
        message: 'Người dùng chưa đăng nhập'
      };
    }

    // Cập nhật profile trong Firebase Auth
    await updateProfile(user, {
      photoURL: imageUrl
    });

    // Cập nhật trong Firestore
    const userDocRef = doc(db, 'users', user.uid);
    await updateDoc(userDocRef, {
      avatar: imageUrl,
      updatedAt: new Date()
    });

    return {
      success: true,
      message: 'Cập nhật avatar thành công',
      imageUrl: imageUrl
    };
  } catch (error) {
    console.error('Error updating user avatar:', error);
    return {
      success: false,
      message: 'Lỗi khi cập nhật avatar'
    };
  }
};

// Hàm chính để xử lý upload và update avatar
export const handleAvatarUpdate = async (imageUri: string): Promise<AvatarUploadResult> => {
  try {
    console.log('Starting avatar update process...');
    
    const user = auth.currentUser;
    if (!user) {
      console.error('No authenticated user found');
      return {
        success: false,
        message: 'Người dùng chưa đăng nhập'
      };
    }

    console.log('User authenticated:', user.uid);
    
    // Kiểm tra user đã verify email chưa (nếu cần)
    if (!user.emailVerified && user.providerData[0]?.providerId === 'password') {
      console.warn('User email not verified, but continuing...');
    }

    // Upload ảnh lên Storage
    console.log('Uploading image to storage...');
    const imageUrl = await uploadImageToStorage(imageUri, user.uid);
    if (!imageUrl) {
      console.log('Upload failed, trying simple upload method...');
      const simpleImageUrl = await uploadImageToStorageSimple(imageUri, user.uid);
      if (!simpleImageUrl) {
        return {
          success: false,
          message: 'Lỗi khi upload ảnh lên server'
        };
      }

      console.log('Simple upload successful, updating user profile...');
      // Cập nhật avatar
      const result = await updateUserAvatar(simpleImageUrl);
      return result;
    }

    console.log('Image uploaded successfully, updating user profile...');
    
    // Cập nhật avatar
    const result = await updateUserAvatar(imageUrl);
    return result;
  } catch (error) {
    console.error('Error handling avatar update:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
    }
    return {
      success: false,
      message: 'Có lỗi xảy ra khi cập nhật avatar'
    };
  }
};
